from uuid import UUID

from sqlalchemy.orm import Session

from app.agentic.models import Thread
from app.common.orm.base_repository import BaseRepository


class ThreadRepository(BaseRepository[Thread]):
    def __init__(self, db_session: Session):
        super().__init__(db_session, Thread)

    def get_by_org_member_id(self, org_member_id: UUID) -> list[Thread] | None:
        res = self._get_by_attrs(organization_member_id=org_member_id)
        return [r for r in res] if res else None

    def get_by_thread_id(self, thread_id: str) -> Thread | None:
        res = self._get_by_attrs(thread_id=thread_id)
        return res[0] if res else None
