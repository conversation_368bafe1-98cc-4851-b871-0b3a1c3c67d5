from typing import Annotated

from fastapi import Depends, Request

from app.agentic.graph.graph_instance import GraphInstance
from app.agentic.graph.graph_manager import GraphManager
from app.agentic.repository import ThreadRepository
from app.agentic.service import AgentService
from app.core.database import DbSessionDep
from app.workspace.dependencies import (
    AuthenticatedUserIdDep,
    OrgMemberIdDep,
    UserIntegrationsDep,
    UserOrgIdDep,
)


def get_graph_manager(request: Request) -> GraphManager:
    if (
        not hasattr(request.app.state, "graph_manager")
        or request.app.state.graph_manager is None
    ):
        raise RuntimeError("GraphManager not initialized.")

    return request.app.state.graph_manager


GraphManagerDep = Annotated[GraphManager, Depends(get_graph_manager)]


def get_org_graph_instance(
    user_id: AuthenticatedUserIdDep,
    user_integrations: UserIntegrationsDep,
    graph_manager: GraphManagerDep,
) -> GraphInstance:
    return graph_manager.get_graph_instance(user_id, user_integrations)


OrgGraphInstanceDep = Annotated[GraphInstance, Depends(get_org_graph_instance)]


def get_agent_service(
    user_id: AuthenticatedUserIdDep,
    org_id: UserOrgIdDep,
    org_member_id: OrgMemberIdDep,
    db_session: DbSessionDep,
    graph_instance: OrgGraphInstanceDep,
) -> AgentService:
    thread_repository = ThreadRepository(db_session)
    return AgentService(
        user_id=user_id,
        org_id=org_id,
        org_member_id=org_member_id,
        db_session=db_session,
        graph_instance=graph_instance,
        thread_repository=thread_repository,
    )


AgentServiceDep = Annotated[AgentService, Depends(get_agent_service)]
