from fastapi import APIRouter, Query
from starlette.responses import StreamingResponse

from app.agentic.dependencies import AgentServiceDep
from app.agentic.schemas import (
    ChatRequest,
    ThreadRead,
)

router = APIRouter()


@router.post("/chat_stream", name="chat_stream")
async def chat_stream(
    request: ChatRequest,
    agent_service: AgentServiceDep,
):
    chat_stream_iterator = await agent_service.process_message_stream(request)

    return StreamingResponse(chat_stream_iterator, media_type="text/event-stream")


@router.get("/threads", name="threads", response_model=list[ThreadRead] | None)
async def get_threads(
    agent_service: AgentServiceDep,
):
    return agent_service.get_threads_by_org_member_id()


@router.get("/thread/{thread_id}", name="thread_history")
async def get_thread_history(
    thread_id: str,
    agent_service: AgentServiceDep,
    page: int = Query(1, ge=1, description="Page number for message history"),
    size: int = Query(20, ge=1, le=100, description="Number of messages per page"),
):
    history_stream_iterator = await agent_service.get_thread_history(
        thread_id, page, size
    )

    return StreamingResponse(history_stream_iterator, media_type="text/event-stream")
