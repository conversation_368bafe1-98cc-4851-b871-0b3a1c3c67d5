import uuid

from sqlalchemy import Foreign<PERSON>ey, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import BaseModel
from app.workspace.models.organization_member import OrganizationMember


class Thread(BaseModel):
    __tablename__ = "threads"

    thread_id: Mapped[str] = mapped_column(
        Text, nullable=False, index=True, unique=True
    )
    organization_member_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization_member.id"),
        nullable=False,
        index=True,
    )

    organization_member: Mapped[OrganizationMember] = relationship("OrganizationMember")

    def __repr__(self):
        return f"<Thread(id='{self.id}', thread_id='{self.thread_id}')>"

    __table_args__ = (
        UniqueConstraint(
            "thread_id", "organization_member_id", name="uq_thread_organization_member"
        ),
    )
