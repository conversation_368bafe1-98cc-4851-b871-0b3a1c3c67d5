from datetime import datetime
from uuid import UUID

from pydantic import BaseModel


class MessageModel(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    message: str
    resume: str | None = None
    thread_id: str | None = None
    crm_account_id: str | None = None


class TokenUsageDetails(BaseModel):
    accepted_prediction_tokens: int = 0
    audio_tokens: int = 0
    reasoning_tokens: int = 0
    rejected_prediction_tokens: int = 0


class PromptTokenDetails(BaseModel):
    audio_tokens: int = 0
    cached_tokens: int = 0


class TokenUsage(BaseModel):
    completion_tokens: int
    prompt_tokens: int
    total_tokens: int
    completion_tokens_details: TokenUsageDetails
    prompt_tokens_details: PromptTokenDetails


class LogProbItem(BaseModel):
    token: str
    bytes: list[int]
    logprob: float
    top_logprobs: list[dict]


class LogProbs(BaseModel):
    content: list[LogProbItem]


class ResponseMetadata(BaseModel):
    token_usage: TokenUsage
    model_name: str
    system_fingerprint: str
    id: str
    finish_reason: str
    logprobs: LogProbs | None = None


class ChatResponse(BaseModel):
    response: str
    metadata: ResponseMetadata
    thread_id: str


class ThreadRead(BaseModel):
    id: UUID
    thread_id: str
    organization_member_id: UUID
    created_at: datetime
    updated_at: datetime
