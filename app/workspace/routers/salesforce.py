import os
from pathlib import Path
from uuid import UUID

import jwt
from fastapi import APIRouter, Query, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from langgraph_sdk.auth.exceptions import HTTPException

from app.common.helpers.logger import get_logger
from app.core.config import config
from app.workspace.dependencies import (
    OrganizationServiceDep,
    SalesforceConnectionServiceDep,
)
from app.workspace.types import EnvironmentType

logger = get_logger()

router = APIRouter()

# Temporary non authenticated - backend version

ROOT_DIR = Path(__file__).resolve().parents[1]
TEMPLATES_DIR = os.path.join(ROOT_DIR, "templates")
templates = Jinja2Templates(directory=TEMPLATES_DIR)


@router.get("/connect/salesforce", response_class=HTMLResponse)
async def connect_salesforce(
    request: Request,
    user_id: UUID,
    org_service: OrganizationServiceDep,
    service: SalesforceConnectionServiceDep,
):
    org = org_service.get_user_organization(user_id)
    if not org:
        raise HTTPException(status_code=404, detail="No organizations found.")

    env = org_service.get_env(org_id=org.id, env_type=EnvironmentType.PROD)
    if not env:
        raise HTTPException(status_code=404, detail="No production environment found.")

    oauth_uri = service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=env,
    )

    return templates.TemplateResponse(
        "connect_salesforce.html",
        {
            "request": request,
            "auth_url": oauth_uri,
        },
    )


@router.get("/auth/salesforce/callback")
async def salesforce_callback(
    org_service: OrganizationServiceDep,
    service: SalesforceConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    payload = jwt.decode(state, config.secret_key, algorithms=["HS256"])
    user_id = UUID(payload["uid"])

    org = org_service.get_user_organization(user_id)
    if not org:
        raise HTTPException(status_code=404, detail="No organizations found.")

    env = org_service.get_env(org_id=org.id, env_type=EnvironmentType.PROD)
    if not env:
        raise HTTPException(status_code=404, detail="No production environment found.")

    crm_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=env,
        code=code,
        state=state,
    )

    return {
        "message": "OAuth succeed with PKCE",
        "token": crm_token.access_token,
        "crm_user_id": crm_token.external_user_id,
        "user_id": user_id,
    }


"""
Authenticated version

@router.get("/connect/salesforce", response_class=HTMLResponse)
async def connect_salesforce(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
):
    oauth_uri = service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
    )

    return templates.TemplateResponse(
        "connect_salesforce.html",
        {
            "auth_url": oauth_uri,
        },
    )


@router.get("/auth/salesforce/callback")
async def salesforce_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    crm_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
    )

    return {
        "message": "OAuth succeed with PKCE",
        "token": crm_token.access_token,
        "crm_user_id": crm_token.external_user_id,
        "user_id": user_id,
    }
"""
