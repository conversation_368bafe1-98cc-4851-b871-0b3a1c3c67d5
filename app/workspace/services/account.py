from uuid import UUID

from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.schemas import AccountRead


class AccountService:
    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def get_accounts(self) -> list[AccountRead]:
        provider, crm_user_id = (
            self.user_integrations.crm(),
            self.user_integrations.crm_user_id,
        )
        if not provider or not crm_user_id:
            return []

        accounts = provider.list_account_access(crm_user_id=crm_user_id)
        return [
            AccountRead(crm_id=account["Id"], crm_name=account["Name"])
            for account in accounts
        ]
