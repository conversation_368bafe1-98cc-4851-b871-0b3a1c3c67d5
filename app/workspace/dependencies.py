from typing import Annotated
from uuid import UUID

from fastapi import Depends

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.core.database import DbSessionDep
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.account import AccountService
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.organization import OrganizationService
from app.workspace.services.organization_team import OrganizationTeamService
from app.workspace.services.salesforce_connection import (
    SalesforceConnectionService,
)
from app.workspace.types import EnvironmentType


def get_organization_service(
    db_session: DbSessionDep,
) -> OrganizationService:
    return OrganizationService(
        db_session=db_session,
        org_repo=OrganizationRepository(db_session),
        env_repo=EnvironmentRepository(db_session),
    )


OrganizationServiceDep = Annotated[
    OrganizationService, Depends(get_organization_service)
]


def get_organization_member_service(
    db_session: DbSessionDep,
) -> OrganizationTeamService:
    return OrganizationTeamService(
        db_session=db_session,
        org_member_repo=OrganizationMemberRepository(db_session),
        org_repo=OrganizationRepository(db_session),
    )


OrganizationTeamServiceDep = Annotated[
    OrganizationTeamService, Depends(get_organization_member_service)
]


def get_integration_cfg_service(
    db_session: DbSessionDep,
) -> IntegrationConfigService:
    return IntegrationConfigService(
        integration_cfg_repo=IntegrationConfigRepository(db_session),
        oauth_token_repo=OAuthTokenRepository(db_session),
    )


IntegrationConfigServiceDep = Annotated[
    IntegrationConfigService, Depends(get_integration_cfg_service)
]


def get_user_org_id(
    user_id: AuthenticatedUserIdDep,
    org_service: OrganizationServiceDep,
) -> UUID:
    org = org_service.get_user_organization(user_id)

    if org:
        return org.id

    raise RuntimeError(f"No organization found for user {user_id}")


UserOrgIdDep = Annotated[UUID, Depends(get_user_org_id)]


def get_org_member_id(
    user_id: AuthenticatedUserIdDep,
    org_member_service: OrganizationTeamServiceDep,
) -> UUID:
    org_member = org_member_service.get_team_member(user_id)

    if org_member:
        return org_member.id

    raise RuntimeError(f"No organization member found for user {user_id}")


OrgMemberIdDep = Annotated[UUID, Depends(get_org_member_id)]


def get_user_env(
    user_org_id: UserOrgIdDep,
    org_service: OrganizationServiceDep,
) -> OrgEnvironment:
    env = org_service.get_env(org_id=user_org_id, env_type=EnvironmentType.PROD)

    if env is None:
        raise RuntimeError(
            f"No production environment found for organization {user_org_id}"
        )

    return env


UserEnvDep = Annotated[OrgEnvironment, Depends(get_user_env)]


def get_salesforce_connection_service(
    db_session: DbSessionDep,
) -> SalesforceConnectionService:
    oauth_token_repo = OAuthTokenRepository(db_session)
    integration_cfg_repo = IntegrationConfigRepository(db_session)

    return SalesforceConnectionService(
        db_session=db_session,
        oauth_token_repo=oauth_token_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.salesforce.auth_url),
        token_url=str(config.salesforce.token_url),
        redirect_uri=str(config.salesforce.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )


SalesforceConnectionServiceDep = Annotated[
    SalesforceConnectionService, Depends(get_salesforce_connection_service)
]


def get_user_integrations(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    integration_cfg_service: IntegrationConfigServiceDep,
    salesforce_connection_service: SalesforceConnectionServiceDep,
    db_session: DbSessionDep,
) -> UserIntegrations:
    return UserIntegrations(
        user_id=user_id,
        environment=user_env,
        integration_cfg_service=integration_cfg_service,
        salesforce_connection_service=salesforce_connection_service,
        db_session=db_session,
    )


UserIntegrationsDep = Annotated[UserIntegrations, Depends(get_user_integrations)]


def get_account_service(
    user_id: AuthenticatedUserIdDep,
    user_integrations: UserIntegrationsDep,
) -> AccountService:
    return AccountService(user_id=user_id, user_integrations=user_integrations)


AccountServiceDep = Annotated[AccountService, Depends(get_account_service)]
