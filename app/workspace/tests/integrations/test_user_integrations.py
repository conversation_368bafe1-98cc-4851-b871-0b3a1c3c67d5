from datetime import datetime
from typing import Any
from uuid import uuid4

import pytest

from app.integrations.factory import IntegrationFactory
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid4(),
        organization_id=uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def mocks(mocker, mock_environment) -> dict[str, Any]:
    integration_config = mocker.MagicMock()
    integration_config.id = uuid4()
    integration_config.source = "salesforce"

    oauth_token = mocker.MagicMock()
    oauth_token.external_user_id = "crm_user_123"
    oauth_token.external_org_id = "crm_org_456"

    integration_cfg_service = mocker.MagicMock(spec=IntegrationConfigService)
    integration_cfg_service.get_crm_config.return_value = integration_config
    integration_cfg_service.get_oauth_token_for_user.return_value = oauth_token

    salesforce_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    crm_provider = mocker.MagicMock()
    mock_integration_factory = mocker.MagicMock(spec=IntegrationFactory)
    mock_integration_factory.create_crm.return_value = crm_provider

    # Au lieu de mocker la méthode _create_factory, on mock la fonction create_factory
    mock_create_factory = mocker.patch(
        "app.workspace.integrations.user_integrations.create_factory",
        return_value=mock_integration_factory,
    )

    return {
        "environment": mock_environment,
        "integration_config": integration_config,
        "oauth_token": oauth_token,
        "integration_cfg_service": integration_cfg_service,
        "salesforce_connection_service": salesforce_connection_service,
        "crm_provider": crm_provider,
        "integration_factory": mock_integration_factory,
        "create_factory_mock": mock_create_factory,
    }


@pytest.fixture
def integrations_params() -> dict[str, Any]:
    return {
        "user_id": uuid4(),
    }


def test_init_integrations(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.user_id == integrations_params["user_id"]
    assert integrations.environment == mocks["environment"]
    assert integrations.org_id == mocks["environment"].organization_id
    assert integrations.env_type == mocks["environment"].type
    assert integrations.db_session == db_session
    assert integrations.integration_cfg_service == mocks["integration_cfg_service"]
    assert (
        integrations.salesforce_connection_service
        == mocks["salesforce_connection_service"]
    )
    assert integrations._factory == mocks["integration_factory"]

    mocks["create_factory_mock"].assert_called_once()

    assert len(integrations._resources) == 0
    assert len(integrations._configs) == 0
    assert len(integrations._tokens) == 0


def test_crm_provider_initialization(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    provider = integrations.crm()

    mocks["integration_cfg_service"].get_crm_config.assert_called_once_with(
        environment=mocks["environment"]
    )

    mocks["integration_cfg_service"].get_oauth_token_for_user.assert_called_once_with(
        integration_config_id=mocks["integration_config"].id,
        user_id=integrations_params["user_id"],
    )

    mocks["integration_factory"].create_crm.assert_called_once_with(
        mocks["integration_config"].source
    )

    assert integrations._resources[IntegrationType.CRM] == mocks["crm_provider"]
    assert provider == mocks["crm_provider"]

    integrations.crm()
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()
    mocks["integration_cfg_service"].get_oauth_token_for_user.assert_called_once()
    mocks["integration_factory"].create_crm.assert_called_once()


def test_crm_user_id_property(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    user_id = integrations.crm_user_id

    assert user_id == mocks["oauth_token"].external_user_id
    assert integrations._tokens[IntegrationType.CRM] == mocks["oauth_token"]

    second_user_id = integrations.crm_user_id
    assert second_user_id == user_id
    mocks["integration_cfg_service"].get_oauth_token_for_user.assert_called_once()


def test_crm_org_id_property(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    org_id = integrations.crm_org_id

    assert org_id == mocks["oauth_token"].external_org_id
    assert integrations._tokens[IntegrationType.CRM] == mocks["oauth_token"]


def test_integration_config_id_property(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    config_id = integrations.integration_config_id

    assert config_id == mocks["integration_config"].id
    assert integrations._configs[IntegrationType.CRM] == mocks["integration_config"]


def test_no_active_crm_integration(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_crm_config.return_value = None

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.crm() is None


def test_no_oauth_token_for_user(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_oauth_token_for_user.return_value = None

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.crm() is None


def test_caching_behavior(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    config = integrations._get_config(IntegrationType.CRM)
    assert config == mocks["integration_config"]
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()

    config = integrations._get_config(IntegrationType.CRM)
    assert config == mocks["integration_config"]
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()

    token = integrations._get_token(IntegrationType.CRM)
    assert token == mocks["oauth_token"]
    mocks["integration_cfg_service"].get_oauth_token_for_user.assert_called_once()

    token = integrations._get_token(IntegrationType.CRM)
    assert token == mocks["oauth_token"]
    mocks["integration_cfg_service"].get_oauth_token_for_user.assert_called_once()


def test_provider_factory_error(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_factory"].create_crm.side_effect = Exception(
        "Provider factory error"
    )

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.crm() is None


def test_unsupported_integration_type(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = integrations._get_resource(IntegrationType.MESSAGING)
    assert result is None


def test_none_config_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    config = integrations._get_config(IntegrationType.MESSAGING)
    assert config is None

    assert IntegrationType.MESSAGING not in integrations._configs


def test_none_token_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_oauth_token_for_user.return_value = None

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    token = integrations._get_token(IntegrationType.CRM)
    assert token is None

    assert IntegrationType.CRM not in integrations._tokens


def test_none_provider_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_oauth_token_for_user.return_value = None

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    provider = integrations.crm()
    assert provider is None

    assert IntegrationType.CRM not in integrations._resources


def test_config_dynamically_available(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_crm_config.return_value = None

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.crm() is None
    assert IntegrationType.CRM not in integrations._configs

    mocks["integration_cfg_service"].get_crm_config.return_value = mocks[
        "integration_config"
    ]

    assert integrations.crm() == mocks["crm_provider"]
    assert integrations._configs[IntegrationType.CRM] == mocks["integration_config"]
