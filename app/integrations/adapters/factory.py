from app.integrations.base.adapter import AdapterType, BaseAdapter
from app.integrations.base.context import <PERSON><PERSON>ontext
from app.integrations.providers.salesforce.adapter import SalesforceAdapter
from app.integrations.providers.salesforce.syncer_adapter import SalesforceSyncerAdapter
from app.integrations.providers.slack.adapter import SlackAdapter
from app.integrations.providers.slack.ingestor_adapter import SlackIngestorAdapter
from app.integrations.providers.slack.processor_adapter import SlackProcessorAdapter
from app.integrations.types import IntegrationSource


class AdapterFactory:
    """Internal factory for creating adapter instances.

    This class manages the mapping between integration types/sources
    and their concrete adapter implementations.
    """

    @classmethod
    def create_adapter(
        cls,
        adapter_type: AdapterType,
        source: IntegrationSource,
        context: BaseContext,
    ) -> BaseAdapter:
        adapter_classes = cls._get_adapter_classes()

        if adapter_type not in adapter_classes:
            raise ValueError(f"Unsupported adapter type: {adapter_type}")

        if source not in adapter_classes[adapter_type]:
            raise ValueError(f"Unsupported source for {adapter_type}: {source}")

        adapter_class = adapter_classes[adapter_type][source]
        return adapter_class(context=context)

    @classmethod
    def _get_adapter_classes(
        cls,
    ) -> dict[AdapterType, dict[IntegrationSource, type[BaseAdapter]]]:
        return {
            AdapterType.CRM: {
                IntegrationSource.SALESFORCE: SalesforceAdapter,
            },
            AdapterType.MESSAGING: {
                IntegrationSource.SLACK: SlackAdapter,
            },
            AdapterType.CRM_SYNCER: {
                IntegrationSource.SALESFORCE: SalesforceSyncerAdapter,
            },
            AdapterType.MESSAGING_INGESTOR: {
                IntegrationSource.SLACK: SlackIngestorAdapter,
            },
            AdapterType.MESSAGING_PROCESSOR: {
                IntegrationSource.SLACK: SlackProcessorAdapter,
            },
        }

    @classmethod
    def get_adapter_types_and_sources(
        cls,
    ) -> dict[AdapterType, list[IntegrationSource]]:
        adapter_classes = cls._get_adapter_classes()
        return {
            adapter_type: list(sources.keys())
            for adapter_type, sources in adapter_classes.items()
        }
