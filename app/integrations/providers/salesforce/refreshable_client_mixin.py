import functools
from collections.abc import Callable
from typing import Any, TypeVar

from simple_salesforce.exceptions import SalesforceExpiredSession

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.providers.salesforce.client import SalesforceClient

logger = get_logger()

T = TypeVar("T")


class SalesforceRefreshableClientMixin:
    credentials: ICredentials
    salesforce_client: SalesforceClient

    def init_salesforce_client(self, credentials: ICredentials):
        self.credentials = credentials

        if "access_token" in self.credentials.secrets:
            # OAuth-based authentication
            if "instance_url" not in self.credentials.secrets:
                error_msg = "Missing required 'instance_url' for Salesforce OAuth authentication"
                logger.error(error_msg)
                raise ValueError(error_msg)

            client_params = {
                "instance_url": self.credentials.secrets["instance_url"],
                "access_token": self.credentials.secrets["access_token"],
            }
            logger.debug("Using OAuth-based authentication for Salesforce")

        elif all(
            k in self.credentials.secrets
            for k in ["username", "password", "security_token"]
        ):
            # Username/password authentication
            client_params = {
                "username": self.credentials.secrets["username"],
                "password": self.credentials.secrets["password"],
                "security_token": self.credentials.secrets["security_token"],
            }
            logger.debug("Using username/password authentication for Salesforce")

        else:
            error_msg = "Missing required Salesforce credentials. Need either username/password/security_token or access_token/instance_url."
            logger.error(error_msg)
            raise ValueError(error_msg)

        self.salesforce_client = SalesforceClient(**client_params)

    def refresh_salesforce_token(self):
        logger.info("Refreshing Salesforce token")
        self.credentials = self.credentials.refresh_token()
        logger.info("Re-initializing Salesforce client with refreshed token")
        self.init_salesforce_client(self.credentials)

    @staticmethod
    def handle_expired_session(method: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(method)
        def wrapper(self, *args: Any, **kwargs: Any) -> T:
            try:
                return method(self, *args, **kwargs)
            except Exception as e:
                current_exception: BaseException | None = e
                while current_exception is not None:
                    if isinstance(current_exception, SalesforceExpiredSession):
                        logger.warning(
                            f"Session expired during {method.__name__}, refreshing token"
                        )
                        self.refresh_salesforce_token()
                        return method(self, *args, **kwargs)

                    current_exception = current_exception.__cause__

                raise

        return wrapper
