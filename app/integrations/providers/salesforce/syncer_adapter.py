from collections.abc import Callable
from typing import Any, cast

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_syncer_adapter import BaseCRMSyncerAdapter
from app.integrations.context import IntegrationContext
from app.integrations.providers.salesforce.sync_handler import SalesforceSyncHandler
from app.integrations.types import IntegrationSource


class SalesforceSyncerAdapter(BaseCRMSyncerAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        ctx = cast("IntegrationContext", self.context)
        credentials_resolver = cast("ICredentialsResolver", ctx.credentials_resolver)
        sync_handler = SalesforceSyncHandler(
            tenant_id=self.context.tenant_id,
            credentials_resolver=credentials_resolver,
            db_session_factory=ctx.db_session_factory,
        )
        return sync_handler.bulk_sync_account_access(
            salesforce_user_ids=crm_user_ids,
            get_credentials_resolver=get_credentials_resolver,
            interval_seconds=interval_seconds,
            daemon_mode=daemon_mode,
        )
