import uuid
from collections.abc import Callable
from enum import Enum
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.providers.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class SalesforceObjectType(str, Enum):
    ACCOUNT = "Account"
    OPPORTUNITY = "Opportunity"


class SalesforceHandler(SalesforceRefreshableClientMixin):
    def __init__(
        self,
        tenant_id: uuid.UUID,
        db_session_factory: Callable,
        credentials_resolver: ICredentialsResolver,
    ):
        self.tenant_id = tenant_id
        self.db_session_factory = db_session_factory
        self.credentials_resolver = credentials_resolver

        credentials = credentials_resolver.get_credentials(
            source=IntegrationSource.SALESFORCE
        )
        self.init_salesforce_client(credentials)

        self._crm_store = PostgresCRMStore(
            tenant_id=tenant_id,
            source=IntegrationSource.SALESFORCE,
            session=db_session_factory(),
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id, fields
        )

        updated_opportunity = self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

        return updated_opportunity

    @SalesforceRefreshableClientMixin.handle_expired_session
    def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.OPPORTUNITY.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_account(self, account_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def list_account_access(
        self, salesforce_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        access_slice = self._crm_store.get_user_account_access(salesforce_user_id)
        paginated_accounts = access_slice.accounts[offset : offset + limit]
        accounts = []
        for account_access in paginated_accounts:
            accounts.append(
                {
                    "Id": account_access.account_id,
                    "Name": account_access.account_name,
                    "AccessType": account_access.access_type,
                    "AccessRole": account_access.access_role or "",
                }
            )

        return accounts
