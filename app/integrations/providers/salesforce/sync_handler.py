import uuid
from collections.abc import Callable
from typing import Any

from app.common.helpers.logger import get_logger
from app.common.pipeline.runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.providers.salesforce.access_resolver import (
    SalesforceAccountAccessResolver,
)
from app.integrations.providers.salesforce.access_sync_stage import (
    SalesforceAccountAccessSyncStage,
)
from app.integrations.providers.salesforce.access_synchronizer import (
    SalesforceAccountAccessSynchronizer,
)
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class SalesforceSyncHandler:
    """
    Service for managing Salesforce data integration.

    This service handles the orchestration of data integration between
    Salesforce and the system, encapsulating the complexity of pipeline
    creation and execution.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        db_session_factory: Callable,
        credentials_resolver: ICredentialsResolver | None = None,
    ):
        self.tenant_id = tenant_id
        self.db_session_factory = db_session_factory
        self.credentials_resolver = credentials_resolver
        self._synchronizer_cache: dict[int, SalesforceAccountAccessSynchronizer] = {}

    def bulk_sync_account_access(
        self,
        salesforce_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        logger.info(
            f"Starting Salesforce account access sync for tenant {self.tenant_id}"
        )

        if get_credentials_resolver is None and self.credentials_resolver is None:
            error_msg = "Either get_credentials_resolver must be provided or credentials_resolver must be set in constructor"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if get_credentials_resolver is None:

            def default_get_credentials_resolver(_):
                return self.credentials_resolver

            get_credentials_resolver = default_get_credentials_resolver

        users_and_synchronizers = []
        failed_users = []

        for user_id in salesforce_user_ids:
            user_resolver = get_credentials_resolver(user_id)

            if user_resolver is None:
                error_msg = f"Missing credentials resolver for user: {user_id}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            try:
                synchronizer = self._get_or_create_synchronizer(user_resolver)

                if synchronizer is not None:
                    users_and_synchronizers.append((user_id, synchronizer))
                else:
                    logger.warning(
                        f"Failed to create synchronizer for user {user_id} due to invalid configuration"
                    )
                    failed_users.append(user_id)
            except Exception as e:
                logger.warning(
                    f"Error creating synchronizer for user {user_id}: {str(e)}"
                )
                failed_users.append(user_id)

        if failed_users:
            logger.warning(
                f"Could not create synchronizers for users: {', '.join(failed_users)}"
            )

        if not users_and_synchronizers:
            logger.error("No valid synchronizers could be created")
            raise ValueError("No valid synchronizers could be created")

        sync_stage = SalesforceAccountAccessSyncStage(
            tenant_id=self.tenant_id,
            db_session=self.db_session_factory(),
            user_synchronizers=users_and_synchronizers,
            interval_seconds=interval_seconds,
            stage_id=f"salesforce_account_access_sync_for_{self.tenant_id}",
        )

        # Create and configure the pipeline
        pipeline = PipelineRunner()
        pipeline.add_stage(sync_stage)

        # Execute the pipeline
        if daemon_mode:
            logger.info("Starting account access sync pipeline in daemon mode")
            pipeline.start_daemon()
            return {"status": "daemon_stopped"}
        else:
            logger.info("Running account access sync pipeline once")
            results = pipeline.run()
            logger.info("Account access sync pipeline execution completed")
            return results

    def _get_or_create_synchronizer(
        self, resolver: ICredentialsResolver
    ) -> SalesforceAccountAccessSynchronizer | None:
        resolver_id = id(resolver)

        if resolver_id in self._synchronizer_cache:
            return self._synchronizer_cache[resolver_id]

        synchronizer = self._create_synchronizer(resolver)

        if synchronizer is not None:
            self._synchronizer_cache[resolver_id] = synchronizer

        return synchronizer

    def _create_synchronizer(
        self,
        creds_resolver: ICredentialsResolver,
    ) -> SalesforceAccountAccessSynchronizer | None:
        if creds_resolver is None:
            return None

        try:
            credentials = creds_resolver.get_credentials(
                source=IntegrationSource.SALESFORCE
            )

            access_resolver = SalesforceAccountAccessResolver(credentials)

            crm_store = PostgresCRMStore(
                tenant_id=self.tenant_id,
                source=IntegrationSource.SALESFORCE,
                session=self.db_session_factory(),
            )

            return SalesforceAccountAccessSynchronizer(
                tenant_id=self.tenant_id,
                crm_store=crm_store,
                access_resolver=access_resolver,
            )

        except Exception:
            logger.exception("Error creating synchronizer")
            return None
