from simple_salesforce import format_soql

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_account_access_resolver import ICRMAccountAccessResolver
from app.integrations.providers.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)
from app.integrations.schemas import CRMAccountAccessData

logger = get_logger()


class SalesforceAccountAccessResolver(
    SalesforceRefreshableClientMixin, ICRMAccountAccessResolver
):
    """
    Default access resolver that retrieves:
    - Accounts the user owns
    - Accounts the user has access to via team membership (if available)
    - Accounts the user has access to via territories (if available)
    """

    def __init__(self, credentials: ICredentials):
        self.init_salesforce_client(credentials)
        self._check_available_objects()

    def refresh_salesforce_token(self):
        super().refresh_salesforce_token()

        try:
            self._update_available_objects()
        except Exception:
            logger.exception("Error checking available objects after token refresh")

    def _update_available_objects(self):
        self._has_team_member = (
            "AccountTeamMember" in self.salesforce_client.available_objects
        )
        self._has_territory = "Territory2" in self.salesforce_client.available_objects

        if not self._has_team_member:
            logger.info(
                "AccountTeamMember object not available in this Salesforce instance"
            )

        if not self._has_territory:
            logger.info("Territory2 object not available in this Salesforce instance")

    @SalesforceRefreshableClientMixin.handle_expired_session
    def _check_available_objects(self):
        self._update_available_objects()

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_user_account_access(self, user_id: str) -> list[CRMAccountAccessData]:
        sf_accounts = self._retrieve_sf_accounts(user_id)

        return [
            CRMAccountAccessData(
                account_id=account["Id"],
                account_name=account.get("Name", ""),
                access_type=account.get("access_type", "unknown"),
                access_role=account.get("role"),
            )
            for account in sf_accounts
        ]

    def _retrieve_sf_accounts(self, user_id: str) -> list[dict]:
        all_accounts = []

        owned_accounts = self._get_owned_accounts(user_id)
        all_accounts.extend(owned_accounts)

        if self._has_team_member:
            team_accounts = self._get_team_accounts(user_id)
            all_accounts.extend(team_accounts)

        if self._has_territory:
            territory_accounts = self._get_territory_accounts(user_id)
            all_accounts.extend(territory_accounts)

        return self._deduplicate_accounts(all_accounts)

    def _get_owned_accounts(self, user_id: str) -> list[dict]:
        owned_accounts = self.salesforce_client.list_objects_by_owner(
            "Account",
            owner_id=user_id,
            fields=["Id", "Name"],
        )

        for account in owned_accounts:
            account["access_type"] = "owner"

        return owned_accounts

    def _get_team_accounts(self, user_id: str) -> list[dict]:
        try:
            if not self._has_team_member:
                logger.debug(
                    "AccountTeamMember object not available, skipping team accounts"
                )
                return []

            team_query = format_soql(
                "SELECT AccountId, TeamMemberRole FROM AccountTeamMember WHERE UserId = {user_id}",
                user_id=user_id,
            )

            team_results = self.salesforce_client.query(team_query)
            team_memberships = team_results.get("records", [])

            if not team_memberships:
                return []

            account_ids = [tm["AccountId"] for tm in team_memberships]
            team_roles = {
                tm["AccountId"]: tm.get("TeamMemberRole") for tm in team_memberships
            }

            team_accounts = self.salesforce_client.list_objects_by_ids(
                "Account",
                account_ids,
                fields=["Id", "Name"],
            )

            for account in team_accounts:
                account["access_type"] = "team"
                account["role"] = team_roles.get(account["Id"])

            return team_accounts
        except Exception:
            logger.exception(f"Error fetching team accounts for {user_id}")
            return []

    def _get_territory_accounts(self, user_id: str) -> list[dict]:
        try:
            if not self._has_territory:
                logger.debug(
                    "Territory2 object not available, skipping territory accounts"
                )
                return []

            objs = self.salesforce_client.available_objects
            if (
                "UserTerritory2Association" not in objs
                or "ObjectTerritory2Association" not in objs
            ):
                logger.debug(
                    "Required territory association objects not available, skipping territory accounts"
                )
                return []

            user_territory_query = format_soql(
                "SELECT Territory2Id "
                "FROM UserTerritory2Association "
                "WHERE UserId = {user_id}",
                user_id=user_id,
            )
            territory_results = self.salesforce_client.query(user_territory_query)
            user_territories = territory_results.get("records", [])
            if not user_territories:
                return []

            territory_ids = [ut["Territory2Id"] for ut in user_territories]

            account_territory_query = format_soql(
                "SELECT Id, Name "
                "FROM Account "
                "WHERE Id IN ("
                "  SELECT ObjectId "
                "  FROM ObjectTerritory2Association "
                "  WHERE Territory2Id IN {territory_ids}"
                ")",
                territory_ids=territory_ids,
            )

            territory_account_results = self.salesforce_client.query(
                account_territory_query
            )
            territory_accounts = territory_account_results.get("records", [])

            for account in territory_accounts:
                account["access_type"] = "territory"

            return territory_accounts

        except Exception as e:
            logger.warning(f"Error fetching territory accounts for {user_id}: {e}")
            return []

    def _deduplicate_accounts(self, all_accounts: list[dict]) -> list[dict]:
        unique_accounts = {}

        priority: dict = {"owner": 0, "team": 1, "territory": 2}

        for account in all_accounts:
            account_id = account["Id"]
            if account_id not in unique_accounts:
                unique_accounts[account_id] = account
            else:
                current_type = unique_accounts[account_id].get("access_type")
                new_type = account.get("access_type", "")

                if priority.get(new_type, 99) < priority.get(current_type, 99):
                    unique_accounts[account_id] = account

        return list(unique_accounts.values())
