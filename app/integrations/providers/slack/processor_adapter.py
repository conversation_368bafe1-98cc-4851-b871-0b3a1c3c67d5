from typing import Any, cast

from app.integrations.base.messaging_processor_adapter import (
    BaseMessagingProcessorAdapter,
)
from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.dataflow_handler import SlackDataflowHandler
from app.integrations.types import IntegrationSource


class SlackProcessorAdapter(BaseMessagingProcessorAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

        if context.credentials_resolver is None:
            raise ValueError(
                "credentials_resolver is required for SlackPipelineAdapter"
            )

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SLACK

    def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        ctx = cast("IntegrationContext", self.context)
        return SlackDataflowHandler.start_channel_processing(
            tenant_id=self.context.tenant_id,
            channel_ids=channel_ids,
            db_session_factory=ctx.db_session_factory,
            interval_seconds=interval_seconds,
            batch_size=batch_size,
            daemon_mode=daemon_mode,
            sequential_execution=sequential_execution,
        )
