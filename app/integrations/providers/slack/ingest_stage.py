import datetime
import uuid

from sqlalchemy.orm import Session

from app.common.helpers.logger import get_logger
from app.common.pipeline.base_stage import BaseStage
from app.integrations.models import SlackIngestionRun
from app.integrations.providers.slack.channel_ingestor import (
    SlackChannelIngestor,
)

logger = get_logger()


class SlackIngestStage(BaseStage):
    """
    Stage for ingesting data from Slack channels using a SlackDataIngestor. Tracking ingestion runs in a database
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        db_session: Session,
        ingestor: SlackChannelIngestor,
        channel_ids: list[str],
        lookback_days: int = 3,
        interval_seconds: int = 60,
        stage_id: str | None = None,
    ):
        """
        Initialize the Slack ingest stage.

        Args:
            tenant_id: The tenant
            db_session: SQLAlchemy session for tracking runs
            ingestor: The Slack data ingestor to use
            channel_ids: List of Slack channel IDs to ingest from
            lookback_days: Number of days to look back for messages
            interval_seconds: Interval between execution cycles in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            stage_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.ingestor = ingestor
        self.channel_ids = channel_ids
        self.lookback_days = lookback_days
        self.tenant_id = tenant_id
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "channels_processed": 0,
            "errors_count": 0,
        }

    def execute_once(self) -> dict:
        """
        Execute one cycle of Slack message ingestion from all configured channels.

        Returns:
            Dictionary with execution results and metrics that follows ExecutionResult structure
        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "channels_processed": 0,
            "channels": {},
        }

        # Calculate time window
        end_time = datetime.datetime.now(datetime.UTC)
        start_time = end_time - datetime.timedelta(days=self.lookback_days)

        try:
            # Process each channel
            for channel_id in self.channel_ids:
                # Create ingestion run
                run = SlackIngestionRun(
                    tenant_id=self.tenant_id,
                    channel_id=channel_id,
                    slice_from_time=start_time,
                    slice_to_time=end_time,
                    status=SlackIngestionRun.Status.IN_PROGRESS,
                    run_start=datetime.datetime.now(datetime.UTC),
                )
                self.db_session.add(run)
                self.db_session.commit()

                try:
                    self.logger.info(
                        f"Ingesting messages from Slack channel {channel_id}"
                    )

                    # Use the ingestor to handle this channel
                    channel_result = self.ingestor.ingest_channel(
                        channel_id=channel_id, start_time=start_time, end_time=end_time
                    )

                    run.status = SlackIngestionRun.Status.SUCCESS
                    run.messages_processed = channel_result.messages_count
                    run.inserts = channel_result.inserts
                    run.updates = channel_result.updates
                    run.deletes = channel_result.deletes
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    self.db_session.commit()

                    # Update statistics
                    results["channels_processed"] += 1
                    self.metrics["channels_processed"] += 1

                    # Add channel-specific results
                    results["channels"][channel_id] = {
                        "status": "success",
                        **channel_result.model_dump(),
                    }

                    self.logger.info(
                        f"Successfully ingested messages from Slack channel {channel_id}"
                    )

                except Exception as e:
                    run.status = SlackIngestionRun.Status.FAILED
                    run.error_message = str(e)
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    self.db_session.commit()

                    error_msg = f"Error ingesting from Slack channel {channel_id}"
                    self.logger.exception(error_msg)
                    results["channels"][channel_id] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # If any channel failed, mark overall status as partial
            if any(ch.get("status") == "error" for ch in results["channels"].values()):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            self.logger.exception(f"Slack ingestion stage failed: {self.stage_id}")
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        """Get detailed status information including metrics."""
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "channels_count": len(self.channel_ids),
                "interval_seconds": self.interval_seconds,
                "metrics": self.metrics,
            }
        )
        return status
