from typing import Any, cast

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.messaging_ingestor_adapter import (
    BaseMessagingIngestorAdapter,
)
from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.dataflow_handler import SlackData<PERSON><PERSON>andler
from app.integrations.types import IntegrationSource


class SlackIngestorAdapter(BaseMessagingIngestorAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

        if context.credentials_resolver is None:
            raise ValueError(
                "credentials_resolver is required for SlackPipelineAdapter"
            )

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SLACK

    def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        ctx = cast("IntegrationContext", self.context)
        credentials_resolver = cast("ICredentialsResolver", ctx.credentials_resolver)
        return SlackDataflowHandler.start_channel_ingestion(
            tenant_id=self.context.tenant_id,
            credentials_resolver=credentials_resolver,
            channel_ids=channel_ids,
            db_session_factory=ctx.db_session_factory,
            interval_seconds=interval_seconds,
            lookback_days=lookback_days,
            batch_size=batch_size,
            daemon_mode=daemon_mode,
            sequential_execution=sequential_execution,
        )
