import uuid
from collections.abc import Callable

from app.common.helpers.logger import get_logger
from app.integrations.processors.embedders.noop_embedder import No<PERSON><PERSON>mbedder
from app.integrations.providers.slack.exceptions import SlackHandlerError
from app.integrations.schemas import DocumentData
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

logger = get_logger()


# rudimentary implementation
class SlackHandler:
    SOURCE = IntegrationSource.SLACK

    def __init__(
        self,
        db_session_factory: Callable,
        tenant_id: uuid.UUID,
    ):
        self._db_session_factory = db_session_factory
        self._tenant_id = tenant_id
        self._embedder = NoopEmbedder()

        self._session = self._db_session_factory()

        self._document_store = PostgresDocumentStore(
            session=self._session, tenant_id=self._tenant_id, source=self.SOURCE
        )

    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        try:
            logger.info(f"Searching for '{query}' in channel {channel_id}")

            query_embedding = self._embedder.embed_text(query)

            channel_tag = f"channel_id:{channel_id}"

            results = self._document_store.find_similar_documents(
                embedding=query_embedding, limit=limit, tag_filter=channel_tag
            )

            return results

        except Exception as e:
            logger.exception(f"Failed to search channel {channel_id}")
            raise SlackHandlerError(f"Failed to search channel: {str(e)}")
