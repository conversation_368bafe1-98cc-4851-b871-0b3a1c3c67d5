from app.common.helpers.logger import get_logger
from app.integrations.base.message_doc_indexer import IMessageToDocumentIndexer
from app.integrations.processors.readers.channel_changelog_reader import (
    ChannelChangelogReader,
)
from app.integrations.schemas import ChannelProcessingResult

logger = get_logger()


class SlackChannelProcessor:
    """
    Processes Slack message changes and regenerates documents.

    This class handles the detection of documents that need to be rebuilt
    based on message changelog, and manages their regeneration or deletion.
    It works directly with the channel changelog manager and document indexer.
    """

    def __init__(
        self,
        channel_changelog_reader: ChannelChangelogReader,
        document_indexer: IMessageToDocumentIndexer,
        batch_size: int = 100,
        consumer_id="slack_channel_processor",
    ):
        """
        Initialize the Slack message processor.

        Args:
            channel_changelog_reader: Store for accessing message changelogs
            document_indexer: Generator for creating documents
            batch_size: Number of changes to process in one batch
            consumer_id: Identifier for the changelog consumer
        """
        self.channel_changelog_reader = channel_changelog_reader
        self.document_indexer = document_indexer
        self.batch_size = batch_size
        self.consumer_id = consumer_id

    def process_channel(self, channel_id: str) -> ChannelProcessingResult:
        """
        Process changes for a specific Slack channel and regenerate documents.

        Args:
            channel_id: The Slack channel ID to process

        Returns:
            ChannelProcessingResult with processing details
        """
        result = ChannelProcessingResult(
            processed_changes=0, regenerated_documents=0, deleted_documents=0
        )

        changelog = self.channel_changelog_reader.get_next_changes(
            channel_id=channel_id,
            consumer_id=self.consumer_id,
            batch_size=self.batch_size,
        )

        # Skip if no changes
        if not changelog.changes:
            return result

        # Process changes
        document_changeset = self.document_indexer.determine_document_changes(
            changelog.changes
        )

        # Delete documents that need to be removed
        for doc_id in document_changeset.documents_to_invalidate:
            self.document_indexer.invalidate_document(doc_id)

        # Regenerate documents
        regenerated_count = 0
        for doc_id in document_changeset.documents_to_rebuild:
            if self.document_indexer.generate_document(doc_id) is not None:
                regenerated_count += 1

        result.processed_changes = len(changelog.changes)
        result.regenerated_documents = regenerated_count
        result.deleted_documents = len(document_changeset.documents_to_invalidate)

        # Update changelog consumer position
        self.channel_changelog_reader.ack(changelog)

        logger.info(
            f"Processed {result.processed_changes} changes for channel {channel_id}: "
            f"regenerated {result.regenerated_documents} documents, "
            f"deleted {result.deleted_documents} documents"
        )

        return result
