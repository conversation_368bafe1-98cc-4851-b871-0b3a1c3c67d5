import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.message_store import IMessageStore
from app.integrations.providers.slack.client import SlackClient
from app.integrations.providers.slack.utils import convert_slack_message_to_message_data
from app.integrations.schemas import ChannelDataSlice, ChannelIngestionResult
from app.integrations.types import IntegrationSource

logger = get_logger()


class SlackChannelIngestor:
    """
    Ingests messages and replies from Slack channels and stores them using IMessageStore.

    This class handles:
    - Retrieving messages and threaded replies from Slack within a configurable time window
    - Handling Slack API rate limits with exponential backoff
    - Reconciling ingested data with existing store
    """

    def __init__(
        self,
        store: IMessageStore,
        credentials_resolver: ICredentialsResolver,
        batch_size: int = 100,
    ):
        """
        Initialize the SlackDataIngestor.

        Args:
            store: The message store implementation to use
            credentials_resolver: Service to provide Slack credentials
            batch_size: Number of messages to fetch in each Slack API call
        """
        self.store = store
        self.credentials_resolver = credentials_resolver

        credentials = credentials_resolver.get_credentials(
            source=IntegrationSource.SLACK
        )
        if "slack_token" not in credentials.secrets:
            error_msg = "Slack token not found in credentials"
            logger.error(error_msg)
            raise ValueError(error_msg)

        self.client = SlackClient(token=credentials.secrets["slack_token"])
        self.batch_size = batch_size

    def ingest_channel(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
    ) -> ChannelIngestionResult:
        """
        Ingest all messages and replies from a Slack channel within the specified time window.

        Args:
            channel_id: The Slack channel ID to ingest
            start_time: Start time for ingestion
            end_time: End time for ingestion

        Returns:
            ChannelIngestionResult with ingestion statistics and metadata
        """
        try:
            self._ensure_joined_channel(channel_id)

            # Fetch messages from the channel
            messages = self._fetch_channel_messages(channel_id, start_time, end_time)

            # Fetch threaded replies for all messages
            all_messages = self._enrich_with_threaded_replies(channel_id, messages)

            # Convert to schema objects
            channel_messages = [
                convert_slack_message_to_message_data(msg, channel_id)
                for msg in all_messages
            ]
            channel_messages.sort(key=lambda msg: msg.sent_at)

            # Create channel slice
            channel_slice = ChannelDataSlice(
                channel_id=channel_id,
                messages=channel_messages,
                from_time=start_time,
                to_time=end_time,
            )

            # Reconcile with store
            stats = self.store.reconcile_channel_messages(channel_slice)

            messages_len = len(all_messages)

            logger.info(
                f"Completed Slack ingestion for channel {channel_id} - "
                f"{messages_len} messages processed: "
                f"{stats.inserts} new, {stats.updates} updated, "
                f"{stats.deletes} deleted."
            )

            return ChannelIngestionResult(
                messages_count=messages_len,
                inserts=stats.inserts,
                updates=stats.updates,
                deletes=stats.deletes,
                from_time=start_time,
                to_time=end_time,
            )

        except Exception:
            logger.exception(f"Error ingesting channel {channel_id}")
            raise

    def _ensure_joined_channel(self, channel_id: str) -> None:
        """
        Ensure the bot is a member of the specified channel.
        Only works for public channels. Private channels must be joined manually.
        """
        try:
            self.client.join_channel(channel_id)
        except Exception:
            logger.exception(f"Error joining channel {channel_id}")
            raise

    def _fetch_channel_messages(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
    ) -> list[dict[str, Any]]:
        """
        Fetch messages from a Slack channel within a time window.

        Excludes:
        - thread_broadcast messages from the main conversation
        - channel_join messages (user joined the channel)
        - other system messages we want to filter

        Args:
            channel_id: The Slack channel ID
            start_time: Start of the time window
            end_time: End of the time window

        Returns:
            List of message objects from Slack API
        """
        # Convert datetimes to Unix timestamps for Slack API
        oldest = str(start_time.timestamp())
        latest = str(end_time.timestamp())

        # Subtypes to filter out
        filtered_subtypes = [
            "thread_broadcast",  # Thread broadcast messages
            "channel_join",  # User joined the channel
            "channel_leave",  # User left the channel
            "channel_archive",  # Channel was archived
            "channel_unarchive",  # Channel was unarchived
        ]

        # Get the channel history
        messages = self.client.get_channel_history(
            channel_id=channel_id,
            limit=self.batch_size,
            oldest=oldest,
            latest=latest,
        )

        # Filter out unwanted message types
        filtered_messages = [
            msg for msg in messages if msg.get("subtype") not in filtered_subtypes
        ]
        filtered_count = len(messages) - len(filtered_messages)

        if filtered_count > 0:
            logger.info(
                f"Filtered out {filtered_count} system messages (channel join/leave etc.)"
            )

        return filtered_messages

    def _enrich_with_threaded_replies(
        self, channel_id: str, messages: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """
        Enrich the messages with their threaded replies.

        Only includes actual replies that are not shared to the channel.
        Excludes "thread_broadcast" messages which are replies that have been
        shared back to the main channel.

        Args:
            channel_id: The Slack channel ID
            messages: List of message objects from Slack API

        Returns:
            List of all messages including threaded replies (excluding broadcast replies)
        """
        all_messages = messages.copy()

        # Find messages with threads
        threaded_messages = self.client.find_threaded_messages(messages)

        # Fetch replies for each threaded message
        for parent in threaded_messages:
            thread_ts = parent.get("ts")
            logger.debug(f"Fetching replies for thread {thread_ts}")

            replies = self.client.get_thread_replies(
                channel_id, thread_ts, self.batch_size
            )

            for reply in replies:
                # Add the reply if it's not already in the messages list
                if not any(msg.get("ts") == reply.get("ts") for msg in all_messages):
                    all_messages.append(reply)

        return all_messages
