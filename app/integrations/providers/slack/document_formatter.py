from app.integrations.base.message_doc_formatter import IMessageDocumentFormatter
from app.integrations.schemas import MessageData


class SlackMessageDocumentFormatter(IMessageDocumentFormatter):
    def format_document_content(
        self,
        main_message: MessageData,
        context_messages: list[MessageData] | None = None,
        replies: list[MessageData] | None = None,
    ) -> str:
        content = main_message.content

        if context_messages:
            content += "\n\nPrevious messages:\n"
            content += "\n".join(msg.content for msg in context_messages)

        if replies:
            content += "\n\nReplies:\n"
            content += "\n".join(msg.content for msg in replies)

        return content
