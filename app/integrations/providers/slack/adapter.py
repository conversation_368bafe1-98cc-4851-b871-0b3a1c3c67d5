from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.handler import <PERSON>lack<PERSON><PERSON><PERSON>
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class SlackAdapter(BaseMessagingAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

        self._handler = SlackHandler(
            db_session_factory=context.db_session_factory,
            tenant_id=context.tenant_id,
        )

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SLACK

    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        return self._handler.search_channel_messages(
            channel_id=channel_id,
            query=query,
            limit=limit,
        )
