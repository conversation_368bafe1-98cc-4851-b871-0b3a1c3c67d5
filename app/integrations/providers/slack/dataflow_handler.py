import uuid
from collections.abc import Callable

from app.common.helpers.logger import get_logger
from app.common.pipeline.runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.processors.embedders.noop_embedder import <PERSON>op<PERSON><PERSON><PERSON>
from app.integrations.processors.indexers.message_context_doc_indexer import (
    MessageWithContextToDocumentIndexer,
)
from app.integrations.processors.readers.channel_changelog_reader import (
    ChannelChangelogReader,
)
from app.integrations.providers.slack.channel_ingestor import (
    SlackChannelIngestor,
)
from app.integrations.providers.slack.channel_processor import (
    SlackChannelProcessor,
)
from app.integrations.providers.slack.document_formatter import (
    SlackMessageDocumentFormatter,
)
from app.integrations.providers.slack.ingest_stage import SlackIngestStage
from app.integrations.providers.slack.process_stage import SlackProcessStage
from app.integrations.stores.pg_cursor_store import PostgresCursorStore
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.stores.pg_message_store import PostgresMessageStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class SlackDataflowHandler:
    """
    Service for managing Slack data integration.

    This service handles the orchestration of data integration between
    Slack and the system, encapsulating the complexity of pipeline
    creation and execution.
    """

    @staticmethod
    def start_channel_ingestion(
        tenant_id: uuid.UUID,
        credentials_resolver: ICredentialsResolver,
        channel_ids: list[str],
        db_session_factory: Callable,
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict:
        """
        Start Slack channel ingestion for the specified channels.

        Args:
            tenant_id: Tenant identifier
            credentials_resolver: Service to provide Slack credentials
            channel_ids: List of Slack channel IDs to ingest
            db_session_factory: Factory function to create database sessions
            interval_seconds: Interval between executions in seconds
            lookback_days: Number of days to look back for messages
            batch_size: Number of messages per Slack API call
            daemon_mode: Whether to run continuously in the background
            sequential_execution: Whether to run stages sequentially in daemon mode

        Returns:
            Results of the pipeline execution if not in daemon mode
            If daemon_mode is True, this method will block until the daemon is stopped,
            then return a status indicating the daemon has stopped.
        """
        logger.info(f"Starting Slack channel ingestion for tenant {tenant_id}")

        # Create store with its own session
        store = PostgresMessageStore(
            tenant_id=tenant_id,
            source=IntegrationSource.SLACK,
            session=db_session_factory(),
        )

        slack_ingestor = SlackChannelIngestor(
            store=store,
            credentials_resolver=credentials_resolver,
            batch_size=batch_size,
        )

        # Create the Slack ingest stage with its own session
        slack_stage = SlackIngestStage(
            tenant_id=tenant_id,
            db_session=db_session_factory(),
            ingestor=slack_ingestor,
            channel_ids=channel_ids,
            lookback_days=lookback_days,
            interval_seconds=interval_seconds,
            stage_id=f"slack_ingestor_for_{tenant_id}",
        )

        # Create and configure the pipeline
        pipeline = PipelineRunner()
        pipeline.add_stage(slack_stage)

        # Execute the pipeline
        if daemon_mode:
            logger.info("Starting channel ingestion pipeline in daemon mode")
            pipeline.start_daemon(
                sequential_execution=sequential_execution,
                sequential_interval_seconds=interval_seconds,
            )
            return {"status": "daemon_stopped"}
        else:
            logger.info("Running channel ingestion pipeline once")
            results = pipeline.run()
            logger.info("Channel ingestion pipeline execution completed")
            return results

    @staticmethod
    def start_channel_processing(
        tenant_id: uuid.UUID,
        channel_ids: list[str],
        db_session_factory,
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = True,
    ) -> dict:
        """
        Start Slack channel processing for the specified channels.

        Args:
            tenant_id: Tenant identifier
            channel_ids: List of Slack channel IDs to process
            db_session_factory: Factory function to create database sessions
            interval_seconds: Interval between executions in seconds
            batch_size: Number of changes to process in one batch
            daemon_mode: Whether to run continuously in the background
            sequential_execution: Whether to run stages sequentially in daemon mode

        Returns:
            Results of the pipeline execution if not in daemon mode
            If daemon_mode is True, this method will block until the daemon is stopped,
            then return a status indicating the daemon has stopped.
        """
        logger.info(f"Starting Slack channel processing for tenant {tenant_id}")

        session = db_session_factory()

        message_store = PostgresMessageStore(
            tenant_id=tenant_id,
            source=IntegrationSource.SLACK,
            session=session,
        )
        cursor_store = PostgresCursorStore(
            tenant_id=tenant_id,
            session=session,
        )
        document_store = PostgresDocumentStore(
            tenant_id=tenant_id,
            session=db_session_factory(),
            source=IntegrationSource.SLACK,
        )

        changelog_reader = ChannelChangelogReader(
            message_store=message_store,
            cursor_store=cursor_store,
        )
        document_indexer = MessageWithContextToDocumentIndexer(
            message_store=message_store,
            document_store=document_store,
            document_formatter=SlackMessageDocumentFormatter(),
            embedder=NoopEmbedder(),
        )

        slack_processor = SlackChannelProcessor(
            channel_changelog_reader=changelog_reader,
            document_indexer=document_indexer,
            batch_size=batch_size,
        )

        slack_stage = SlackProcessStage(
            tenant_id=tenant_id,
            db_session=db_session_factory(),
            processor=slack_processor,
            channel_ids=channel_ids,
            interval_seconds=interval_seconds,
            stage_id=f"slack_processor_for_{tenant_id}",
        )

        pipeline = PipelineRunner()
        pipeline.add_stage(slack_stage)

        # Execute the pipeline
        if daemon_mode:
            logger.info("Starting channel processing pipeline in daemon mode")
            pipeline.start_daemon(
                sequential_execution=sequential_execution,
                sequential_interval_seconds=interval_seconds,
            )
            return {"status": "daemon_stopped"}
        else:
            logger.info("Running channel processing pipeline once")
            results = pipeline.run()
            logger.info("Channel processing pipeline execution completed")
            return results
