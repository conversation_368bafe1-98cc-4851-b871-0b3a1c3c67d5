from app.core.database import <PERSON>Local
from app.integrations.adapters.factory import AdapterFactory
from app.integrations.base.adapter import AdapterType
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.base.crm_syncer_adapter import BaseCRMSyncerAdapter
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.base.messaging_ingestor_adapter import (
    BaseMessagingIngestorAdapter,
)
from app.integrations.base.messaging_processor_adapter import (
    BaseMessagingProcessorAdapter,
)
from app.integrations.context import IntegrationContext, IntegrationContextFactory
from app.integrations.protocols import (
    CRMResource,
    CRMSyncerResource,
    MessagingIngestorResource,
    MessagingProcessorResource,
    MessagingResource,
)
from app.integrations.types import IntegrationSource


class IntegrationFactory:
    """Factory for creating integration resources.

    This is the main entry point for obtaining integration resources.
    """

    def __init__(self, context: IntegrationContext):
        self.context = context

    def create_crm(self, source: IntegrationSource) -> CRMResource:
        adapter = AdapterFactory.create_adapter(
            adapter_type=AdapterType.CRM, source=source, context=self.context
        )

        if not isinstance(adapter, BaseCRMAdapter):
            raise TypeError("Created adapter does not implement CRM interface")

        return adapter

    def create_messaging(self, source: IntegrationSource) -> MessagingResource:
        adapter = AdapterFactory.create_adapter(
            adapter_type=AdapterType.MESSAGING, source=source, context=self.context
        )

        if not isinstance(adapter, BaseMessagingAdapter):
            raise TypeError("Created adapter does not implement Messaging interface")

        return adapter

    def create_crm_syncer(self, source: IntegrationSource) -> CRMSyncerResource:
        adapter = AdapterFactory.create_adapter(
            adapter_type=AdapterType.CRM_SYNCER, source=source, context=self.context
        )

        if not isinstance(adapter, BaseCRMSyncerAdapter):
            raise TypeError("Created adapter does not implement CRM syncer interface")

        return adapter

    def create_messaging_ingestor(
        self, source: IntegrationSource
    ) -> MessagingIngestorResource:
        adapter = AdapterFactory.create_adapter(
            adapter_type=AdapterType.MESSAGING_INGESTOR,
            source=source,
            context=self.context,
        )

        if not isinstance(adapter, BaseMessagingIngestorAdapter):
            raise TypeError(
                "Created adapter does not implement Messaging ingestor interface"
            )

        return adapter

    def create_messaging_processor(
        self, source: IntegrationSource
    ) -> MessagingProcessorResource:
        adapter = AdapterFactory.create_adapter(
            adapter_type=AdapterType.MESSAGING_PROCESSOR,
            source=source,
            context=self.context,
        )

        if not isinstance(adapter, BaseMessagingProcessorAdapter):
            raise TypeError(
                "Created adapter does not implement Messaging processor interface"
            )

        return adapter

    @classmethod
    def get_supported_sources(
        cls, provider_type: AdapterType | None = None
    ) -> dict[AdapterType, list[IntegrationSource]]:
        all_sources = AdapterFactory.get_adapter_types_and_sources()

        if provider_type:
            if provider_type not in all_sources:
                return {provider_type: []}
            return {provider_type: all_sources[provider_type]}

        return all_sources

    @classmethod
    def is_source_supported(
        cls, provider_type: AdapterType, source: IntegrationSource
    ) -> bool:
        supported = cls.get_supported_sources(provider_type)
        return source in supported.get(provider_type, [])


def create_factory(
    tenant_id, db_factory=None, credentials_resolver=None
) -> IntegrationFactory:
    if db_factory is None:
        db_factory = SessionLocal

    context = IntegrationContextFactory.create_context(
        tenant_id=tenant_id,
        db_session_factory=db_factory,
        credentials_resolver=credentials_resolver,
    )

    return IntegrationFactory(context)
