from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import AdapterType, BaseAdapter


class BaseMessagingIngestorAdapter(BaseAdapter, ABC):
    @property
    def adapter_type(self) -> AdapterType:
        return AdapterType.MESSAGING_INGESTOR

    def get_capabilities(self) -> list[str]:
        return [
            "start_channel_ingestion",
        ]

    @abstractmethod
    def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        pass
