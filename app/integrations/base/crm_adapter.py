from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import AdapterType, BaseAdapter


class BaseCRMAdapter(BaseAdapter, ABC):
    @property
    def adapter_type(self) -> AdapterType:
        return AdapterType.CRM

    def get_capabilities(self) -> list[str]:
        return [
            "get_opportunity",
            "update_opportunity",
            "list_opportunities",
            "get_account",
            "list_accounts",
        ]

    @abstractmethod
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    def get_account(self, account_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def list_account_access(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        pass
