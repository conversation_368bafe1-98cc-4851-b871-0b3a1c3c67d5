from abc import ABC, abstractmethod

from app.integrations.base.adapter import AdapterType, BaseAdapter
from app.integrations.schemas import DocumentData


class BaseMessagingAdapter(BaseAdapter, ABC):
    @property
    def adapter_type(self) -> AdapterType:
        return AdapterType.MESSAGING

    def get_capabilities(self) -> list[str]:
        return [
            "search_channel_messages",
        ]

    @abstractmethod
    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        pass
