import uuid
from abc import ABC, abstractmethod

from app.integrations.base.context import BaseContext
from app.integrations.types import AdapterType, IntegrationSource


class BaseAdapter(ABC):
    def __init__(self, context: BaseContext):
        self._context = context

    @property
    @abstractmethod
    def source(self) -> IntegrationSource:
        pass

    @property
    @abstractmethod
    def adapter_type(self) -> AdapterType:
        pass

    @property
    def tenant_id(self) -> uuid.UUID:
        return self._context.tenant_id

    @property
    def context(self) -> BaseContext:
        return self._context

    @abstractmethod
    def get_capabilities(self) -> list[str]:
        pass
