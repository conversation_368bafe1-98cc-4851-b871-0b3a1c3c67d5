from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import AdapterType, BaseAdapter


class BaseMessagingProcessorAdapter(BaseAdapter, ABC):
    @property
    def adapter_type(self) -> AdapterType:
        return AdapterType.MESSAGING_PROCESSOR

    def get_capabilities(self) -> list[str]:
        return [
            "start_channel_processing",
        ]

    @abstractmethod
    def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        pass
