from abc import ABC, abstractmethod
from collections.abc import Callable
from typing import Any

from app.integrations.base.adapter import AdapterType, BaseAdapter
from app.integrations.base.credentials_resolver import ICredentialsResolver


class BaseCRMSyncerAdapter(BaseAdapter, ABC):
    @property
    def adapter_type(self) -> AdapterType:
        return AdapterType.CRM_SYNCER

    def get_capabilities(self) -> list[str]:
        return [
            "bulk_sync_account_access",
        ]

    @abstractmethod
    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        pass
