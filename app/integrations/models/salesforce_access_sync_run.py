import datetime
import enum

from sqlalchemy import DateTime, Integer, String
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel


class SalesforceAccessSyncRun(TenantModel):
    __tablename__ = "salesforce_access_sync_run"

    class Status(str, enum.Enum):
        IN_PROGRESS = "in_progress"
        SUCCESS = "success"
        FAILED = "failed"

    salesforce_user_id: Mapped[str] = mapped_column(String, nullable=False)
    status: Mapped[Status] = mapped_column(
        StringEnum(Status, length=20), nullable=False
    )
    new_access_count: Mapped[int] = mapped_column(Integer, default=0)
    old_access_count: Mapped[int] = mapped_column(Integer, default=0)
    error_message: Mapped[str | None] = mapped_column(String(1024), nullable=True)
    run_start: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    run_end: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
