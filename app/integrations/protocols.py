from collections.abc import Callable
from typing import Any, Protocol

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class BaseIntegrationResource(Protocol):
    """Base protocol for all integration resources."""

    @property
    def source(self) -> IntegrationSource:
        """Gets the source integration."""
        ...

    def get_capabilities(self) -> list[str]:
        """Gets the capabilities of this provider."""
        ...


class CRMResource(BaseIntegrationResource, Protocol):
    """Protocol defining the interface for CRM providers."""

    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        """Gets an opportunity by ID."""
        ...

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates an opportunity."""
        ...

    def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists opportunities for an account."""
        ...

    def get_account(self, account_id: str) -> dict[str, Any]:
        """Gets an account by ID."""
        ...

    def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists accounts that the specified user has access to."""
        ...


class CRMSyncerResource(BaseIntegrationResource, Protocol):
    """Protocol defining the interface for CRM synchronization operations."""

    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        """Starts synchronization of account access for the specified users."""
        ...


class MessagingResource(BaseIntegrationResource, Protocol):
    """Protocol defining the interface for messaging providers."""

    def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        """Searches for messages in the specified channel."""
        ...


class MessagingIngestorResource(BaseIntegrationResource, Protocol):
    """Protocol defining the interface for messaging ingestion operations."""

    def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        """Starts ingestion of messages from the specified channels."""
        ...


class MessagingProcessorResource(BaseIntegrationResource, Protocol):
    """Protocol defining the interface for messaging processing operations."""

    def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
        sequential_execution: bool = False,
    ) -> dict[str, Any]:
        """Starts processing of messages from the specified channels."""
        ...
