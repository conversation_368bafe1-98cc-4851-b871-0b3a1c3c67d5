import uuid

import pytest

from app.core.database import SessionLocal
from app.integrations.adapters.factory import AdapterFactory
from app.integrations.base.adapter import AdapterType
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.base.crm_syncer_adapter import Base<PERSON><PERSON>ync<PERSON>Adapter
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.base.messaging_ingestor_adapter import (
    BaseMessagingIngestorAdapter,
)
from app.integrations.base.messaging_processor_adapter import (
    BaseMessagingProcessorAdapter,
)
from app.integrations.context import IntegrationContext, IntegrationContextFactory
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.types import IntegrationSource


def implements_crm_adapter(obj):
    required_attrs = [
        "source",
        "get_capabilities",
        "get_opportunity",
        "update_opportunity",
        "list_opportunities_by_account",
        "get_account",
        "list_account_access",
    ]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_messaging_adapter(obj):
    required_attrs = ["source", "get_capabilities", "search_channel_messages"]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_crm_syncer_adapter(obj):
    required_attrs = ["source", "get_capabilities", "bulk_sync_account_access"]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_messaging_ingestor_adapter(obj):
    required_attrs = ["source", "get_capabilities", "start_channel_ingestion"]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_messaging_processor_adapter(obj):
    required_attrs = ["source", "get_capabilities", "start_channel_processing"]
    return all(hasattr(obj, attr) for attr in required_attrs)


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mocker.Mock()
    return mock_context


@pytest.fixture(autouse=True)
def mock_adapter_factory(mocker):
    return mocker.patch.object(AdapterFactory, "create_adapter")


@pytest.fixture
def mock_integration_factory(mock_context):
    return IntegrationFactory(context=mock_context)


def test_create_crm(mocker, mock_integration_factory, mock_adapter_factory):
    mock_crm_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter_factory.return_value = mock_crm_adapter

    result = mock_integration_factory.create_crm(source=IntegrationSource.SALESFORCE)

    mock_adapter_factory.assert_called_once_with(
        adapter_type=AdapterType.CRM,
        source=IntegrationSource.SALESFORCE,
        context=mock_integration_factory.context,
    )
    assert result == mock_crm_adapter
    assert implements_crm_adapter(result), (
        "Object does not implement CRM adapter interface"
    )


def test_create_messaging(mocker, mock_integration_factory, mock_adapter_factory):
    mock_messaging_adapter = mocker.Mock(spec=BaseMessagingAdapter)
    mock_adapter_factory.return_value = mock_messaging_adapter

    result = mock_integration_factory.create_messaging(source=IntegrationSource.SLACK)

    mock_adapter_factory.assert_called_once_with(
        adapter_type=AdapterType.MESSAGING,
        source=IntegrationSource.SLACK,
        context=mock_integration_factory.context,
    )
    assert result == mock_messaging_adapter
    assert implements_messaging_adapter(result), (
        "Object does not implement Messaging adapter interface"
    )


def test_create_crm_syncer(mocker, mock_integration_factory, mock_adapter_factory):
    mock_crm_syncer_adapter = mocker.Mock(spec=BaseCRMSyncerAdapter)
    mock_adapter_factory.return_value = mock_crm_syncer_adapter

    result = mock_integration_factory.create_crm_syncer(
        source=IntegrationSource.SALESFORCE
    )

    mock_adapter_factory.assert_called_once_with(
        adapter_type=AdapterType.CRM_SYNCER,
        source=IntegrationSource.SALESFORCE,
        context=mock_integration_factory.context,
    )
    assert result == mock_crm_syncer_adapter
    assert implements_crm_syncer_adapter(result), (
        "Object does not implement CRM syncer adapter interface"
    )


def test_create_messaging_ingestor(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_messaging_ingestor_adapter = mocker.Mock(spec=BaseMessagingIngestorAdapter)
    mock_adapter_factory.return_value = mock_messaging_ingestor_adapter

    result = mock_integration_factory.create_messaging_ingestor(
        source=IntegrationSource.SLACK
    )

    mock_adapter_factory.assert_called_once_with(
        adapter_type=AdapterType.MESSAGING_INGESTOR,
        source=IntegrationSource.SLACK,
        context=mock_integration_factory.context,
    )
    assert result == mock_messaging_ingestor_adapter
    assert implements_messaging_ingestor_adapter(result), (
        "Object does not implement Messaging ingestor adapter interface"
    )


def test_create_messaging_processor(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_messaging_processor_adapter = mocker.Mock(spec=BaseMessagingProcessorAdapter)
    mock_adapter_factory.return_value = mock_messaging_processor_adapter

    result = mock_integration_factory.create_messaging_processor(
        source=IntegrationSource.SLACK
    )

    mock_adapter_factory.assert_called_once_with(
        adapter_type=AdapterType.MESSAGING_PROCESSOR,
        source=IntegrationSource.SLACK,
        context=mock_integration_factory.context,
    )
    assert result == mock_messaging_processor_adapter
    assert implements_messaging_processor_adapter(result), (
        "Object does not implement Messaging processor adapter interface"
    )


def test_create_crm_wrong_type(mocker, mock_integration_factory, mock_adapter_factory):
    mock_messaging_adapter = mocker.Mock(spec=BaseMessagingAdapter)
    mock_adapter_factory.return_value = mock_messaging_adapter

    with pytest.raises(
        TypeError, match="Created adapter does not implement CRM interface"
    ):
        mock_integration_factory.create_crm(source=IntegrationSource.SALESFORCE)


def test_create_messaging_wrong_type(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_crm_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter_factory.return_value = mock_crm_adapter

    with pytest.raises(
        TypeError, match="Created adapter does not implement Messaging interface"
    ):
        mock_integration_factory.create_messaging(source=IntegrationSource.SLACK)


def test_create_crm_syncer_wrong_type(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_messaging_adapter = mocker.Mock(spec=BaseMessagingAdapter)
    mock_adapter_factory.return_value = mock_messaging_adapter

    with pytest.raises(
        TypeError, match="Created adapter does not implement CRM syncer interface"
    ):
        mock_integration_factory.create_crm_syncer(source=IntegrationSource.SALESFORCE)


def test_create_messaging_ingestor_wrong_type(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_crm_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter_factory.return_value = mock_crm_adapter

    with pytest.raises(
        TypeError,
        match="Created adapter does not implement Messaging ingestor interface",
    ):
        mock_integration_factory.create_messaging_ingestor(
            source=IntegrationSource.SLACK
        )


def test_create_messaging_processor_wrong_type(
    mocker, mock_integration_factory, mock_adapter_factory
):
    mock_crm_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter_factory.return_value = mock_crm_adapter

    with pytest.raises(
        TypeError,
        match="Created adapter does not implement Messaging processor interface",
    ):
        mock_integration_factory.create_messaging_processor(
            source=IntegrationSource.SLACK
        )


def test_create_supported_sources(mocker):
    expected_sources = {
        AdapterType.CRM: [IntegrationSource.SALESFORCE],
        AdapterType.MESSAGING: [IntegrationSource.SLACK],
        AdapterType.CRM_SYNCER: [IntegrationSource.SALESFORCE],
        AdapterType.MESSAGING_INGESTOR: [IntegrationSource.SLACK],
        AdapterType.MESSAGING_PROCESSOR: [IntegrationSource.SLACK],
    }
    mocker.patch.object(
        AdapterFactory, "get_adapter_types_and_sources", return_value=expected_sources
    )

    result = IntegrationFactory.get_supported_sources()

    assert result == expected_sources
    AdapterFactory.get_adapter_types_and_sources.assert_called_once()


def test_create_supported_sources_with_filter(mocker):
    all_sources = {
        AdapterType.CRM: [IntegrationSource.SALESFORCE],
        AdapterType.MESSAGING: [IntegrationSource.SLACK],
    }
    mocker.patch.object(
        AdapterFactory, "get_adapter_types_and_sources", return_value=all_sources
    )

    result = IntegrationFactory.get_supported_sources(provider_type=AdapterType.CRM)

    assert result == {AdapterType.CRM: [IntegrationSource.SALESFORCE]}
    AdapterFactory.get_adapter_types_and_sources.assert_called_once()


def test_create_supported_sources_with_unsupported_type(mocker):
    all_sources = {
        AdapterType.CRM: [IntegrationSource.SALESFORCE],
    }
    mocker.patch.object(
        AdapterFactory, "get_adapter_types_and_sources", return_value=all_sources
    )

    result = IntegrationFactory.get_supported_sources(provider_type=AdapterType.FILE)

    assert result == {AdapterType.FILE: []}
    AdapterFactory.get_adapter_types_and_sources.assert_called_once()


def test_is_source_supported(mocker):
    all_sources = {
        AdapterType.CRM: [IntegrationSource.SALESFORCE],
        AdapterType.MESSAGING: [IntegrationSource.SLACK],
    }
    mocker.patch.object(
        IntegrationFactory, "get_supported_sources", return_value=all_sources
    )

    assert (
        IntegrationFactory.is_source_supported(
            AdapterType.CRM, IntegrationSource.SALESFORCE
        )
        is True
    )
    assert (
        IntegrationFactory.is_source_supported(AdapterType.CRM, IntegrationSource.SLACK)
        is False
    )
    assert (
        IntegrationFactory.is_source_supported(
            AdapterType.FILE, IntegrationSource.SALESFORCE
        )
        is False
    )


def test_create_factory_with_defaults(mocker, tenant_id, mock_context):
    mocker.patch.object(
        IntegrationContextFactory, "create_context", return_value=mock_context
    )

    factory_instance = mocker.Mock(spec=IntegrationFactory)
    factory_class_mock = mocker.patch(
        "app.integrations.factory.IntegrationFactory", return_value=factory_instance
    )

    result = create_factory(tenant_id)

    IntegrationContextFactory.create_context.assert_called_once_with(
        tenant_id=tenant_id,
        db_session_factory=SessionLocal,
        credentials_resolver=None,
    )

    factory_class_mock.assert_called_once_with(mock_context)
    assert result == factory_instance


def test_create_factory_with_custom_params(mocker, tenant_id, mock_context):
    mocker.patch.object(
        IntegrationContextFactory, "create_context", return_value=mock_context
    )

    factory_instance = mocker.Mock(spec=IntegrationFactory)
    factory_class_mock = mocker.patch(
        "app.integrations.factory.IntegrationFactory", return_value=factory_instance
    )

    def custom_db_factory():
        return "custom_db_session"

    custom_credentials_resolver = mocker.Mock()

    result = create_factory(
        tenant_id=tenant_id,
        db_factory=custom_db_factory,
        credentials_resolver=custom_credentials_resolver,
    )

    IntegrationContextFactory.create_context.assert_called_once_with(
        tenant_id=tenant_id,
        db_session_factory=custom_db_factory,
        credentials_resolver=custom_credentials_resolver,
    )

    factory_class_mock.assert_called_once_with(mock_context)
    assert result == factory_instance


def test_create_factory_integration(tenant_id):
    def custom_db_factory():
        return None

    result = create_factory(tenant_id, db_factory=custom_db_factory)

    assert isinstance(result, IntegrationFactory)
    assert isinstance(result.context, IntegrationContext)
    assert result.context.tenant_id == tenant_id
    assert result.context.db_session_factory == custom_db_factory
    assert result.context.credentials_resolver is None
