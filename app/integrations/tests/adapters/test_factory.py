import uuid

import pytest

from app.integrations.adapters.factory import AdapterFactory
from app.integrations.base.adapter import AdapterType
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.base.crm_syncer_adapter import BaseCRMSyncerAdapter
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.base.messaging_ingestor_adapter import (
    BaseMessagingIngestorAdapter,
)
from app.integrations.base.messaging_processor_adapter import (
    BaseMessagingProcessorAdapter,
)
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mocker.Mock()
    return mock_context


def test_get_adapter_classes():
    adapter_classes = AdapterFactory._get_adapter_classes()

    assert AdapterType.MESSAGING in adapter_classes
    assert AdapterType.CRM in adapter_classes
    assert AdapterType.MESSAGING_INGESTOR in adapter_classes
    assert AdapterType.MESSAGING_PROCESSOR in adapter_classes
    assert AdapterType.CRM_SYNCER in adapter_classes

    assert IntegrationSource.SLACK in adapter_classes[AdapterType.MESSAGING]
    assert IntegrationSource.SALESFORCE in adapter_classes[AdapterType.CRM]
    assert IntegrationSource.SLACK in adapter_classes[AdapterType.MESSAGING_INGESTOR]
    assert IntegrationSource.SLACK in adapter_classes[AdapterType.MESSAGING_PROCESSOR]
    assert IntegrationSource.SALESFORCE in adapter_classes[AdapterType.CRM_SYNCER]


def test_create_adapter(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseCRMAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.CRM: {IntegrationSource.SALESFORCE: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.CRM,
        source=IntegrationSource.SALESFORCE,
        context=mock_context,
    )

    mock_adapter_class.assert_called_once_with(context=mock_context)
    assert result == mock_adapter


def test_create_adapter_unsupported_type(mock_context):
    with pytest.raises(ValueError, match="Unsupported adapter type"):
        AdapterFactory.create_adapter(
            adapter_type=AdapterType.FILE,
            source=IntegrationSource.SALESFORCE,
            context=mock_context,
        )


def test_create_adapter_unsupported_source(mock_context):
    with pytest.raises(ValueError, match="Unsupported source for"):
        AdapterFactory.create_adapter(
            adapter_type=AdapterType.CRM,
            source="UNKNOWN_SOURCE",
            context=mock_context,
        )


def test_get_adapter_types_and_sources():
    result = AdapterFactory.get_adapter_types_and_sources()

    assert AdapterType.CRM in result
    assert AdapterType.MESSAGING in result
    assert AdapterType.CRM_SYNCER in result
    assert AdapterType.MESSAGING_INGESTOR in result
    assert AdapterType.MESSAGING_PROCESSOR in result

    assert IntegrationSource.SALESFORCE in result[AdapterType.CRM]
    assert IntegrationSource.SLACK in result[AdapterType.MESSAGING]
    assert IntegrationSource.SALESFORCE in result[AdapterType.CRM_SYNCER]
    assert IntegrationSource.SLACK in result[AdapterType.MESSAGING_INGESTOR]
    assert IntegrationSource.SLACK in result[AdapterType.MESSAGING_PROCESSOR]


def test_crm_adapter_creation(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseCRMAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.CRM: {IntegrationSource.SALESFORCE: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.CRM,
        source=IntegrationSource.SALESFORCE,
        context=mock_context,
    )

    assert isinstance(result, mocker.MagicMock)
    assert result._spec_class == BaseCRMAdapter


def test_messaging_adapter_creation(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseMessagingAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.MESSAGING: {IntegrationSource.SLACK: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.MESSAGING,
        source=IntegrationSource.SLACK,
        context=mock_context,
    )

    assert isinstance(result, mocker.MagicMock)
    assert result._spec_class == BaseMessagingAdapter


def test_crm_syncer_adapter_creation(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseCRMSyncerAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.CRM_SYNCER: {IntegrationSource.SALESFORCE: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.CRM_SYNCER,
        source=IntegrationSource.SALESFORCE,
        context=mock_context,
    )

    assert isinstance(result, mocker.MagicMock)
    assert result._spec_class == BaseCRMSyncerAdapter


def test_messaging_ingestor_adapter_creation(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseMessagingIngestorAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.MESSAGING_INGESTOR: {IntegrationSource.SLACK: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.MESSAGING_INGESTOR,
        source=IntegrationSource.SLACK,
        context=mock_context,
    )

    assert isinstance(result, mocker.MagicMock)
    assert result._spec_class == BaseMessagingIngestorAdapter


def test_messaging_processor_adapter_creation(mocker, mock_context):
    mock_adapter = mocker.MagicMock(spec=BaseMessagingProcessorAdapter)
    mock_adapter_class = mocker.MagicMock(return_value=mock_adapter)

    adapter_classes_patch = {
        AdapterType.MESSAGING_PROCESSOR: {IntegrationSource.SLACK: mock_adapter_class}
    }
    mocker.patch.object(
        AdapterFactory, "_get_adapter_classes", return_value=adapter_classes_patch
    )

    result = AdapterFactory.create_adapter(
        adapter_type=AdapterType.MESSAGING_PROCESSOR,
        source=IntegrationSource.SLACK,
        context=mock_context,
    )

    assert isinstance(result, mocker.MagicMock)
    assert result._spec_class == BaseMessagingProcessorAdapter
