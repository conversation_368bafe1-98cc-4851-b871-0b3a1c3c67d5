# ruff: noqa: S106
import uuid
from datetime import datetime

import pytest

from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.adapter import SlackAdapter
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = None
    return mock_context


@pytest.fixture(autouse=True)
def mock_slack_handler(mocker):
    mock_handler = mocker.MagicMock()

    doc = DocumentData(
        id="doc1",
        content="test content",
        metadata={"author": "user1"},
        source_timestamp=datetime.now(),
        tags=["tag1", "tag2"],
    )
    mock_handler.search_channel_messages.return_value = [(doc, 0.95)]

    handler_class = mocker.patch(
        "app.integrations.providers.slack.adapter.SlackHandler"
    )
    handler_class.return_value = mock_handler

    return mock_handler


@pytest.fixture
def slack_adapter(mock_context):
    return SlackAdapter(context=mock_context)


def test_init(mocker, mock_context):
    mock_handler = mocker.patch("app.integrations.providers.slack.adapter.SlackHandler")

    adapter = SlackAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id

    mock_handler.assert_called_once_with(
        db_session_factory=mock_context.db_session_factory,
        tenant_id=mock_context.tenant_id,
    )


def test_source(slack_adapter):
    assert slack_adapter.source == IntegrationSource.SLACK


def test_search_channel_messages(slack_adapter, mock_slack_handler):
    channel_id = "C123"
    query = "test query"
    limit = 5

    result = slack_adapter.search_channel_messages(
        channel_id=channel_id,
        query=query,
        limit=limit,
    )

    mock_slack_handler.search_channel_messages.assert_called_once_with(
        channel_id=channel_id,
        query=query,
        limit=limit,
    )

    assert len(result) == 1
    assert result[0][0].id == "doc1"
    assert result[0][0].content == "test content"
    assert result[0][1] == 0.95
