# ruff: noqa: S106
import uuid

import pytest

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.ingestor_adapter import (
    SlackIngestorAdapter,
)
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = {"token": "xoxb-test-token"}
    return mock_service


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def slack_ingestor_adapter(mock_context):
    return SlackIngestorAdapter(context=mock_context)


def test_init(mock_context):
    adapter = SlackIngestorAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id


def test_source(slack_ingestor_adapter):
    assert slack_ingestor_adapter.source == IntegrationSource.SLACK


def test_start_channel_ingestion(mocker, slack_ingestor_adapter, mock_context):
    mock_ingestion = mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.SlackDataflowHandler.start_channel_ingestion"
    )
    mock_ingestion.return_value = {"status": "success"}

    channel_ids = ["C123", "C456"]
    interval_seconds = 600
    lookback_days = 14
    batch_size = 200
    daemon_mode = True
    sequential_execution = True

    result = slack_ingestor_adapter.start_channel_ingestion(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        lookback_days=lookback_days,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    mock_ingestion.assert_called_once_with(
        tenant_id=mock_context.tenant_id,
        credentials_resolver=mock_context.credentials_resolver,
        channel_ids=channel_ids,
        db_session_factory=mock_context.db_session_factory,
        interval_seconds=interval_seconds,
        lookback_days=lookback_days,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    assert result == {"status": "success"}
