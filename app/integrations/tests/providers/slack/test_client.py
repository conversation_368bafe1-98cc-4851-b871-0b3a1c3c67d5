# ruff: noqa: S106
import pytest
from slack_sdk.errors import SlackA<PERSON><PERSON>rror

from app.integrations.providers.slack.client import <PERSON>lack<PERSON><PERSON>, SlackClientError


@pytest.fixture
def slack_client(mocker):
    mock_web_client = mocker.MagicMock()
    mocker.patch(
        "app.integrations.providers.slack.client.WebClient",
        return_value=mock_web_client,
    )

    client = SlackClient(token="xoxb-test-token")
    # Return both the client and the mock web client for easier testing
    return client, mock_web_client


def test_init():
    client = SlackClient(token="xoxb-test-token")
    assert client.token == "xoxb-test-token"


def test_lazy_client_initialization(mocker):
    mock_create = mocker.patch.object(
        SlackClient, "_create_client", return_value=mocker.MagicMock()
    )

    client = SlackClient(token="xoxb-test-token")
    # Should not be called yet
    mock_create.assert_not_called()

    # Access triggers creation
    _ = client._client
    mock_create.assert_called_once()

    # Subsequent access doesn't create again
    _ = client._client
    mock_create.assert_called_once()


def test_create_client_error(mocker):
    mocker.patch(
        "app.integrations.providers.slack.client.WebClient",
        side_effect=Exception("Connection error"),
    )

    client = SlackClient(token="xoxb-test-token")
    with pytest.raises(SlackClientError, match="Client initialization failed"):
        _ = client._client


def test_find_threaded_messages():
    messages = [
        {"ts": "111.222", "text": "Regular message"},
        {"ts": "222.333", "thread_ts": "222.333", "text": "Thread parent"},
        {"ts": "333.444", "thread_ts": "222.333", "text": "Thread reply"},
        {"ts": "444.555", "thread_ts": "444.555", "text": "Another thread parent"},
    ]

    threaded_messages = SlackClient.find_threaded_messages(messages)

    assert len(threaded_messages) == 2
    assert threaded_messages[0]["ts"] == "222.333"
    assert threaded_messages[1]["ts"] == "444.555"


def test_get_channel_info_success(slack_client):
    client, mock_web_client = slack_client

    expected_response = {
        "channel": {
            "id": "C12345",
            "name": "general",
            "is_channel": True,
            "is_private": False,
        }
    }
    mock_web_client.conversations_info.return_value = expected_response

    result = client.get_channel_info("C12345")

    assert result == expected_response["channel"]
    mock_web_client.conversations_info.assert_called_once_with(channel="C12345")


def test_get_channel_info_error(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "channel_not_found"
    error = SlackApiError("Channel not found", mock_response)

    mock_web_client.conversations_info.side_effect = error

    with pytest.raises(SlackClientError, match="Slack API error"):
        client.get_channel_info("C12345")


def test_join_channel_success(slack_client):
    client, mock_web_client = slack_client

    mock_web_client.conversations_join.return_value = {"ok": True}

    client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_join_channel_already_in_channel(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError for already_in_channel
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "already_in_channel"
    error = SlackApiError("Already in channel", mock_response)

    mock_web_client.conversations_join.side_effect = error

    # Should not raise an exception
    client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_join_channel_private_channel(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError for private channel
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "method_not_supported_for_channel_type"
    error = SlackApiError("Private channel", mock_response)

    mock_web_client.conversations_join.side_effect = error

    # Should not raise an exception for private channels
    client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_join_channel_missing_scope(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError for missing scope
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "missing_scope"
    error = SlackApiError("Missing scope", mock_response)

    mock_web_client.conversations_join.side_effect = error

    with pytest.raises(SlackClientError, match="Missing scope to join channel"):
        client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_join_channel_channel_not_found(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError for channel_not_found
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "channel_not_found"
    error = SlackApiError("Channel not found", mock_response)

    mock_web_client.conversations_join.side_effect = error

    with pytest.raises(SlackClientError, match="Channel not found"):
        client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_join_channel_unknown_error(slack_client, mocker):
    client, mock_web_client = slack_client

    # Create a mock SlackApiError for unknown error
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "unknown_error"
    error = SlackApiError("Unknown error", mock_response)

    mock_web_client.conversations_join.side_effect = error

    with pytest.raises(SlackClientError, match="Failed to join channel"):
        client.join_channel("C12345")

    mock_web_client.conversations_join.assert_called_once_with(channel="C12345")


def test_get_channel_history_success(slack_client):
    client, mock_web_client = slack_client

    messages = [
        {"ts": "111.222", "text": "Message 1"},
        {"ts": "111.333", "text": "Message 2"},
    ]

    mock_web_client.conversations_history.return_value = {
        "messages": messages,
        "response_metadata": {},  # No next_cursor
    }

    result = client.get_channel_history("C12345")

    assert len(result) == 2
    assert result[0]["ts"] == "111.222"
    assert result[1]["ts"] == "111.333"

    mock_web_client.conversations_history.assert_called_once_with(
        channel="C12345",
        limit=100,
        oldest=None,
        latest=None,
        cursor=None,
    )


def test_get_channel_history_with_params(slack_client):
    client, mock_web_client = slack_client

    mock_web_client.conversations_history.return_value = {
        "messages": [{"ts": "111.222", "text": "Message 1"}],
        "response_metadata": {},
    }

    result = client.get_channel_history(
        channel_id="C12345",
        limit=10,
        oldest="111.000",
        latest="222.000",
    )

    assert len(result) == 1

    mock_web_client.conversations_history.assert_called_once_with(
        channel="C12345",
        limit=10,
        oldest="111.000",
        latest="222.000",
        cursor=None,
    )


def test_get_channel_history_pagination(slack_client):
    client, mock_web_client = slack_client

    page1 = {
        "messages": [{"ts": "111.222", "text": "Message 1"}],
        "response_metadata": {"next_cursor": "next-page"},
    }

    page2 = {
        "messages": [{"ts": "222.333", "text": "Message 2"}],
        "response_metadata": {},  # No cursor, end of pagination
    }

    mock_web_client.conversations_history.side_effect = [page1, page2]

    result = client.get_channel_history("C12345")

    assert len(result) == 2
    assert result[0]["ts"] == "111.222"
    assert result[1]["ts"] == "222.333"

    assert mock_web_client.conversations_history.call_count == 2

    # Check first call
    first_call_kwargs = mock_web_client.conversations_history.call_args_list[0][1]
    assert first_call_kwargs["channel"] == "C12345"
    assert first_call_kwargs["cursor"] is None

    # Check second call uses cursor from first response
    second_call_kwargs = mock_web_client.conversations_history.call_args_list[1][1]
    assert second_call_kwargs["cursor"] == "next-page"


def test_get_channel_history_respects_limit(slack_client):
    client, mock_web_client = slack_client

    page1 = {
        "messages": [{"ts": f"111.{i}", "text": f"Message {i}"} for i in range(80)],
        "response_metadata": {"next_cursor": "next-page"},
    }

    page2 = {
        "messages": [
            {"ts": f"222.{i}", "text": f"Message {i + 80}"} for i in range(80)
        ],
        "response_metadata": {"next_cursor": "should-not-get-here"},
    }

    mock_web_client.conversations_history.side_effect = [page1, page2]

    result = client.get_channel_history("C12345", limit=100)

    # Should get exactly 100 messages
    assert len(result) == 100
    assert result[0]["ts"] == "111.0"
    assert result[99]["ts"] == "222.19"  # 80 from first page + 20 from second

    # Should make 2 calls to the API
    assert mock_web_client.conversations_history.call_count == 2


def test_get_thread_replies_success(slack_client):
    client, mock_web_client = slack_client

    mock_web_client.conversations_replies.return_value = {
        "messages": [
            {"ts": "111.222", "text": "Parent message"},  # Should be skipped
            {"ts": "111.333", "text": "Reply 1"},
            {"ts": "111.444", "text": "Reply 2"},
        ],
        "response_metadata": {},
    }

    result = client.get_thread_replies("C12345", "111.222")

    # First message (parent) should be skipped
    assert len(result) == 2
    assert result[0]["ts"] == "111.333"
    assert result[1]["ts"] == "111.444"

    mock_web_client.conversations_replies.assert_called_once_with(
        channel="C12345",
        ts="111.222",
        limit=100,
        cursor=None,
    )


def test_get_thread_replies_pagination(slack_client):
    client, mock_web_client = slack_client

    page1 = {
        "messages": [
            {"ts": "111.222", "text": "Parent message"},  # Should be skipped
            {"ts": "111.333", "text": "Reply 1"},
        ],
        "response_metadata": {"next_cursor": "next-page"},
    }

    page2 = {
        "messages": [
            {"ts": "111.222", "text": "Parent again"},  # Not skipped on 2nd page
            {"ts": "111.444", "text": "Reply 2"},
        ],
        "response_metadata": {},
    }

    mock_web_client.conversations_replies.side_effect = [page1, page2]

    result = client.get_thread_replies("C12345", "111.222")

    # Parent is only skipped on first page
    assert len(result) == 3
    assert result[0]["ts"] == "111.333"  # Reply from page 1
    assert result[1]["ts"] == "111.222"  # Parent message from page 2 (not skipped)
    assert result[2]["ts"] == "111.444"  # Reply from page 2

    assert mock_web_client.conversations_replies.call_count == 2

    # Check pagination
    first_call_kwargs = mock_web_client.conversations_replies.call_args_list[0][1]
    assert first_call_kwargs["cursor"] is None

    second_call_kwargs = mock_web_client.conversations_replies.call_args_list[1][1]
    assert second_call_kwargs["cursor"] == "next-page"


def test_get_thread_replies_respects_limit(slack_client):
    client, mock_web_client = slack_client

    page1 = {
        "messages": [
            {"ts": "111.222", "text": "Parent message"},  # Skipped
            *[{"ts": f"111.{i + 300}", "text": f"Reply {i}"} for i in range(50)],
        ],
        "response_metadata": {"next_cursor": "next-page"},
    }

    page2 = {
        "messages": [
            {"ts": "111.222", "text": "Parent again"},  # Not skipped
            *[{"ts": f"111.{i + 400}", "text": f"Reply {i + 50}"} for i in range(100)],
        ],
        "response_metadata": {"next_cursor": "should-not-get-here"},
    }

    mock_web_client.conversations_replies.side_effect = [page1, page2]

    result = client.get_thread_replies("C12345", "111.222", limit=60)

    # Should have exactly 60 messages
    assert len(result) == 60

    # Should make 2 calls to the API
    assert mock_web_client.conversations_replies.call_count == 2


def test_handle_slack_api_errors_with_retry_success(slack_client, mocker):
    client, _ = slack_client

    # Create a test function with the decorator
    test_func = mocker.Mock(return_value="success")
    decorated_func = SlackClient._handle_slack_api_errors_with_retry()(test_func)

    result = decorated_func("arg1", kwarg1="value1")

    assert result == "success"
    test_func.assert_called_once_with("arg1", kwarg1="value1")


def test_handle_slack_api_errors_with_retry_rate_limited(slack_client, mocker):
    client, _ = slack_client

    # Create mock responses for rate limiting
    mock_response1 = mocker.MagicMock()
    mock_response1.get.return_value = "ratelimited"
    mock_response1.headers = {"Retry-After": "0.01"}

    mock_response2 = mocker.MagicMock()
    mock_response2.get.return_value = "ratelimited"
    mock_response2.headers = {"Retry-After": "0.01"}

    # Create a test function that fails with rate limiting then succeeds
    test_func = mocker.Mock(
        side_effect=[
            SlackApiError("Rate limited", mock_response1),
            SlackApiError("Rate limited", mock_response2),
            "success",
        ]
    )

    # Create the decorated function
    decorated_func = SlackClient._handle_slack_api_errors_with_retry(
        initial_wait=0.01, max_retries=3
    )(test_func)

    # Mock sleep to avoid actual waiting
    mock_sleep = mocker.patch("time.sleep")

    # Execute the function
    result = decorated_func()

    # Should succeed after retries
    assert result == "success"
    assert test_func.call_count == 3
    assert mock_sleep.call_count == 2


def test_handle_slack_api_errors_with_retry_max_retries_exceeded(slack_client, mocker):
    client, _ = slack_client

    # Create a rate limiting error
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "ratelimited"
    mock_response.headers = {"Retry-After": "0.01"}
    rate_limit_error = SlackApiError("Rate limited", mock_response)

    # Create function that always fails with rate limiting
    test_func = mocker.Mock(side_effect=rate_limit_error)

    # Create decorated function with 2 retries
    decorated_func = SlackClient._handle_slack_api_errors_with_retry(
        initial_wait=0.01, max_retries=2
    )(test_func)

    # Mock sleep to avoid waiting
    mocker.patch("time.sleep")

    # Should fail after max retries
    with pytest.raises(SlackClientError, match="Rate limit retry attempts exhausted"):
        decorated_func()

    # Should have attempted 3 times total (initial + 2 retries)
    assert test_func.call_count == 3


def test_handle_slack_api_errors_with_retry_other_slack_error(slack_client, mocker):
    client, _ = slack_client

    # Create a non-rate-limit Slack error
    mock_response = mocker.MagicMock()
    mock_response.get.return_value = "invalid_auth"
    error = SlackApiError("Invalid auth", mock_response)

    # Create function that fails with non-rate-limit error
    test_func = mocker.Mock(side_effect=error)

    # Create decorated function
    decorated_func = SlackClient._handle_slack_api_errors_with_retry()(test_func)

    # Should convert to SlackClientError without retrying
    with pytest.raises(SlackClientError, match="Slack API error"):
        decorated_func()

    # Should have been called only once (no retries)
    assert test_func.call_count == 1


def test_handle_slack_api_errors_with_retry_unexpected_error(slack_client, mocker):
    client, _ = slack_client

    # Create function that fails with a generic error
    test_func = mocker.Mock(side_effect=ValueError("Something went wrong"))

    # Create decorated function
    decorated_func = SlackClient._handle_slack_api_errors_with_retry()(test_func)

    # Should convert to SlackClientError without retrying
    with pytest.raises(SlackClientError, match="Unexpected error"):
        decorated_func()

    # Should have been called only once (no retries)
    assert test_func.call_count == 1
