import datetime

import pytest

from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.providers.slack.channel_ingestor import (
    SlackChannelIngestor,
)
from app.integrations.providers.slack.client import Slack<PERSON>lient, SlackClientError
from app.integrations.schemas import Channel<PERSON><PERSON><PERSON>R<PERSON>ult
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {"slack_token": "xoxb-dummy-token"}
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = mock_credentials
    return mock_service


@pytest.fixture
def slack_ingestor(mocker, mock_credentials_resolver):
    store = mocker.Mock()
    store.reconcile_channel_messages.return_value = mocker.Mock(
        inserts=5, updates=2, deletes=1
    )

    # Mock the SlackClient
    mock_client = mocker.Mock(spec=SlackClient)
    mocker.patch(
        "app.integrations.providers.slack.channel_ingestor.SlackClient",
        return_value=mock_client,
    )

    ingestor = SlackChannelIngestor(
        store=store,
        credentials_resolver=mock_credentials_resolver,
        batch_size=100,
    )
    return ingestor


def test_ingest_channel_success(slack_ingestor, mocker):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    mocker.patch.object(
        slack_ingestor,
        "_fetch_channel_messages",
        return_value=[{"ts": "123.456", "text": "Hello"}],
    )
    mocker.patch.object(
        slack_ingestor,
        "_enrich_with_threaded_replies",
        return_value=[
            {"ts": "123.456", "text": "Hello"},
            {"ts": "123.789", "text": "Reply", "thread_ts": "123.456"},
        ],
    )

    result = slack_ingestor.ingest_channel(channel_id, start_time, end_time)

    assert isinstance(result, ChannelIngestionResult)
    assert result.messages_count == 2
    assert result.inserts == 5
    assert result.updates == 2
    assert result.deletes == 1

    slack_ingestor.store.reconcile_channel_messages.assert_called_once()


def test_ingest_channel_messages_sorted(slack_ingestor, mocker):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    initial_messages = [
        {"ts": "123.789", "text": "Later message"},
        {"ts": "123.456", "text": "Earlier message", "thread_ts": "123.456"},
    ]

    thread_replies = [
        {"ts": "123.900", "text": "Late reply", "thread_ts": "123.456"},
        {"ts": "123.500", "text": "Early reply", "thread_ts": "123.456"},
    ]

    mocker.patch.object(
        slack_ingestor, "_fetch_channel_messages", return_value=initial_messages
    )
    mocker.patch.object(
        slack_ingestor,
        "_enrich_with_threaded_replies",
        return_value=initial_messages + thread_replies,
    )

    reconcile_spy = mocker.spy(slack_ingestor.store, "reconcile_channel_messages")

    slack_ingestor.ingest_channel(channel_id, start_time, end_time)

    assert reconcile_spy.call_count == 1
    channel_slice = reconcile_spy.call_args[0][0]

    message_timestamps = [msg.sent_at for msg in channel_slice.messages]
    assert message_timestamps == sorted(message_timestamps), (
        "Messages should be sorted chronologically"
    )

    expected_contents = [
        "Earlier message",
        "Early reply",
        "Later message",
        "Late reply",
    ]
    assert [msg.content for msg in channel_slice.messages] == expected_contents


def test_ingest_channel_error(slack_ingestor, mocker):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    mocker.patch.object(
        slack_ingestor, "_fetch_channel_messages", side_effect=Exception("API error")
    )

    with pytest.raises(Exception, match="API error"):
        slack_ingestor.ingest_channel(channel_id, start_time, end_time)


def test_fetch_channel_messages(slack_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    # Set up the mock response from the client
    all_messages = [
        {"ts": "111.222", "text": "Message 1"},
        {"ts": "111.333", "text": "Broadcast", "subtype": "thread_broadcast"},
        {"ts": "111.444", "text": "Message 2"},
        {"ts": "222.333", "text": "Message 3"},
        {"ts": "222.444", "text": "Message 4"},
    ]

    slack_ingestor.client.get_channel_history.return_value = all_messages

    messages = slack_ingestor._fetch_channel_messages(channel_id, start_time, end_time)

    # Should have filtered out the thread_broadcast message
    assert len(messages) == 4
    assert messages[0]["ts"] == "111.222"
    assert messages[1]["ts"] == "111.444"  # Note the broadcast message is filtered out
    assert messages[2]["ts"] == "222.333"
    assert messages[3]["ts"] == "222.444"

    # Verify the client was called with the correct parameters
    slack_ingestor.client.get_channel_history.assert_called_once_with(
        channel_id=channel_id,
        limit=100,
        oldest=str(start_time.timestamp()),
        latest=str(end_time.timestamp()),
    )


def test_credentials_resolver_used(mock_credentials_resolver, mocker):
    store = mocker.Mock()

    mock_client = mocker.Mock(spec=SlackClient)
    mocker.patch(
        "app.integrations.providers.slack.channel_ingestor.SlackClient",
        return_value=mock_client,
    )

    SlackChannelIngestor(
        store=store,
        credentials_resolver=mock_credentials_resolver,
        batch_size=100,
    )

    mock_credentials_resolver.get_credentials.assert_called_once_with(
        source=IntegrationSource.SLACK
    )


def test_missing_slack_token(mocker):
    store = mocker.Mock()

    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value.secrets = {}

    mock_client = mocker.Mock(spec=SlackClient)
    mocker.patch(
        "app.integrations.providers.slack.channel_ingestor.SlackClient",
        return_value=mock_client,
    )

    with pytest.raises(ValueError, match="Slack token not found in credentials"):
        SlackChannelIngestor(
            store=store,
            credentials_resolver=mock_service,
            batch_size=100,
        )


def test_filtered_subtypes_in_fetch_channel_messages(slack_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    # Set up the mock response with various subtypes
    messages_with_filtered_subtypes = [
        {"ts": "111.222", "text": "Regular message"},
        {
            "ts": "111.333",
            "text": "Thread broadcast",
            "subtype": "thread_broadcast",
        },
        {"ts": "111.444", "text": "User joined", "subtype": "channel_join"},
        {"ts": "111.555", "text": "User left", "subtype": "channel_leave"},
        {"ts": "111.666", "text": "Channel archived", "subtype": "channel_archive"},
        {
            "ts": "111.777",
            "text": "Channel unarchived",
            "subtype": "channel_unarchive",
        },
        {"ts": "111.888", "text": "Another regular message"},
    ]

    slack_ingestor.client.get_channel_history.return_value = (
        messages_with_filtered_subtypes
    )

    messages = slack_ingestor._fetch_channel_messages(channel_id, start_time, end_time)

    assert len(messages) == 2
    assert messages[0]["ts"] == "111.222"
    assert messages[1]["ts"] == "111.888"


def test_enrich_with_threaded_replies(slack_ingestor):
    channel_id = "C12345"
    messages = [
        {"ts": "111.222", "text": "Thread parent", "thread_ts": "111.222"},
        {"ts": "222.333", "text": "Regular message"},
    ]

    thread_replies = [
        {"ts": "333.444", "text": "Reply 1", "thread_ts": "111.222"},
        {"ts": "333.555", "text": "Reply 2", "thread_ts": "111.222"},
    ]

    # Set up the mocks for the client's methods
    slack_ingestor.client.find_threaded_messages.return_value = [messages[0]]
    slack_ingestor.client.get_thread_replies.return_value = thread_replies

    enriched_messages = slack_ingestor._enrich_with_threaded_replies(
        channel_id, messages
    )

    assert len(enriched_messages) == 4
    assert enriched_messages[0]["ts"] == "111.222"  # Original thread parent
    assert enriched_messages[1]["ts"] == "222.333"  # Original regular message
    assert enriched_messages[2]["ts"] == "333.444"  # First reply
    assert enriched_messages[3]["ts"] == "333.555"  # Second reply

    slack_ingestor.client.find_threaded_messages.assert_called_once_with(messages)
    slack_ingestor.client.get_thread_replies.assert_called_once_with(
        channel_id, "111.222", slack_ingestor.batch_size
    )


def test_ensure_joined_channel_success(slack_ingestor):
    channel_id = "C12345"

    slack_ingestor._ensure_joined_channel(channel_id)

    slack_ingestor.client.join_channel.assert_called_once_with(channel_id)


def test_ensure_joined_channel_error(slack_ingestor):
    channel_id = "C12345"

    # Set up a SlackClientError when joining channel
    slack_ingestor.client.join_channel.side_effect = SlackClientError(
        "Error joining channel"
    )

    # Should raise the exception
    with pytest.raises(SlackClientError, match="Error joining channel"):
        slack_ingestor._ensure_joined_channel(channel_id)

    slack_ingestor.client.join_channel.assert_called_once_with(channel_id)
