import uuid

from app.common.pipeline.runner import <PERSON><PERSON>ine<PERSON>unner
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.providers.slack.dataflow_handler import SlackDataflowHandler
from app.integrations.providers.slack.ingest_stage import SlackIngestStage


def test_start_channel_ingestion_normal_mode(mocker):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.run.return_value = {"status": "success", "details": "test results"}
    mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    tenant_id = uuid.uuid4()
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value.secrets = {
        "slack_token": "test_token"
    }
    channel_ids = ["channel1", "channel2"]
    db_session_factory = mocker.Mock()

    result = SlackDataflowHandler.start_channel_ingestion(
        tenant_id=tenant_id,
        credentials_resolver=mock_credentials_resolver,
        channel_ids=channel_ids,
        db_session_factory=db_session_factory,
        daemon_mode=False,
    )

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.run.call_count == 1
    assert result == {"status": "success", "details": "test results"}


def test_start_channel_ingestion_daemon_mode(mocker):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    tenant_id = uuid.uuid4()
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value.secrets = {
        "slack_token": "test_token"
    }
    channel_ids = ["channel1", "channel2"]
    db_session_factory = mocker.Mock()

    result = SlackDataflowHandler.start_channel_ingestion(
        tenant_id=tenant_id,
        credentials_resolver=mock_credentials_resolver,
        channel_ids=channel_ids,
        db_session_factory=db_session_factory,
        daemon_mode=True,
    )

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.start_daemon.call_count == 1
    assert result == {"status": "daemon_stopped"}


def test_start_channel_ingestion_custom_params(mocker):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.run.return_value = {"status": "success"}
    mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    mock_stage = mocker.Mock(spec=SlackIngestStage)
    stage_init_mock = mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.SlackIngestStage",
        return_value=mock_stage,
    )

    tenant_id = uuid.uuid4()
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value.secrets = {
        "slack_token": "test_token"
    }
    channel_ids = ["channel1", "channel2"]
    db_session_factory = mocker.Mock()
    custom_interval = 600
    custom_lookback = 14
    custom_batch_size = 200

    SlackDataflowHandler.start_channel_ingestion(
        tenant_id=tenant_id,
        credentials_resolver=mock_credentials_resolver,
        channel_ids=channel_ids,
        db_session_factory=db_session_factory,
        interval_seconds=custom_interval,
        lookback_days=custom_lookback,
        batch_size=custom_batch_size,
    )

    assert stage_init_mock.call_count == 1
    _, kwargs = stage_init_mock.call_args
    assert kwargs["interval_seconds"] == custom_interval
    assert kwargs["lookback_days"] == custom_lookback
    assert kwargs["channel_ids"] == channel_ids


def test_start_channel_processing_normal_mode(mocker):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.run.return_value = {"status": "success", "details": "test results"}
    mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    tenant_id = uuid.uuid4()
    channel_ids = ["channel1", "channel2"]
    db_session_factory = mocker.Mock()

    result = SlackDataflowHandler.start_channel_processing(
        tenant_id=tenant_id,
        channel_ids=channel_ids,
        db_session_factory=db_session_factory,
        daemon_mode=False,
    )

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.run.call_count == 1
    assert result == {"status": "success", "details": "test results"}


def test_start_channel_processing_daemon_mode(mocker):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    tenant_id = uuid.uuid4()
    channel_ids = ["channel1", "channel2"]
    db_session_factory = mocker.Mock()

    result = SlackDataflowHandler.start_channel_processing(
        tenant_id=tenant_id,
        channel_ids=channel_ids,
        db_session_factory=db_session_factory,
        daemon_mode=True,
    )

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.start_daemon.call_count == 1
    assert result == {"status": "daemon_stopped"}
