import uuid

from app.common.pipeline.base_stage import BaseStage
from app.integrations.models import SlackProcessingRun
from app.integrations.providers.slack.process_stage import SlackProcessStage
from app.integrations.schemas import ChannelProcessingResult


def test_execute_once_success(mocker):
    channel_ids = ["C123", "C456"]
    tenant_id = uuid.uuid4()

    slack_processor = mocker.Mock()

    result1 = ChannelProcessingResult(
        processed_changes=10, regenerated_documents=11, deleted_documents=9
    )
    result2 = ChannelProcessingResult(
        processed_changes=20, regenerated_documents=21, deleted_documents=19
    )

    slack_processor.process_channel.side_effect = [result1, result2]

    session = mocker.Mock()

    stage = SlackProcessStage(
        tenant_id=tenant_id,
        db_session=session,
        processor=slack_processor,
        channel_ids=channel_ids,
        interval_seconds=60,
    )

    result = stage.execute_once()

    assert result["status"] == "success"
    assert result["channels_processed"] == 2

    channel_result = result["channels"].get("C123")
    assert channel_result is not None
    assert channel_result["status"] == "success"
    assert channel_result["processed_changes"] == 10
    assert channel_result["regenerated_documents"] == 11
    assert channel_result["deleted_documents"] == 9

    channel_result = result["channels"].get("C456")
    assert channel_result is not None
    assert channel_result["status"] == "success"
    assert channel_result["processed_changes"] == 20
    assert channel_result["regenerated_documents"] == 21
    assert channel_result["deleted_documents"] == 19

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["channels_processed"] == 2
    assert stage.metrics["successful_runs"] == 1
    assert stage.metrics["errors_count"] == 0

    call_kwargs_list = slack_processor.process_channel.call_args_list
    assert len(call_kwargs_list) == 2

    assert session.add.call_count == 2
    assert session.commit.call_count == 4


def test_execute_once_partial(mocker, db_session):
    channel_ids = ["C123", "C456"]
    tenant_id = uuid.uuid4()

    slack_processor = mocker.Mock()

    def fake_process_channel(channel):
        if channel == "C456":
            raise Exception("Execute error")
        return ChannelProcessingResult(
            processed_changes=10, regenerated_documents=11, deleted_documents=9
        )

    slack_processor.process_channel.side_effect = fake_process_channel

    stage = SlackProcessStage(
        tenant_id=tenant_id,
        db_session=db_session,
        processor=slack_processor,
        channel_ids=channel_ids,
        interval_seconds=60,
    )

    result = stage.execute_once()

    assert result["status"] == "partial"
    assert result["channels_processed"] == 1

    channel_res_ok = result["channels"].get("C123")
    assert channel_res_ok is not None
    assert channel_res_ok["status"] == "success"
    assert channel_res_ok["processed_changes"] == 10
    assert channel_res_ok["regenerated_documents"] == 11
    assert channel_res_ok["deleted_documents"] == 9

    channel_res_err = result["channels"].get("C456")
    assert channel_res_err is not None
    assert channel_res_err["status"] == "error"
    assert "Execute error" in channel_res_err["error"]

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["channels_processed"] == 1
    assert stage.metrics["successful_runs"] == 0
    assert stage.metrics["errors_count"] == 1

    run_entry_count = db_session.query(SlackProcessingRun).count()
    assert run_entry_count == 2
    run_entry = (
        db_session.query(SlackProcessingRun)
        .filter(SlackProcessingRun.channel_id == "C123")
        .first()
    )
    assert run_entry is not None
    assert run_entry.status == SlackProcessingRun.Status.SUCCESS
    run_entry = (
        db_session.query(SlackProcessingRun)
        .filter(SlackProcessingRun.channel_id == "C456")
        .first()
    )
    assert run_entry is not None
    assert run_entry.status == SlackProcessingRun.Status.FAILED


def test_get_status(mocker):
    channel_ids = ["C123", "C456"]
    tenant_id = uuid.UUID("12345678-1234-5678-1234-************")

    slack_processor = mocker.Mock()

    dummy_base_status = {"base": True}
    mocker.patch.object(BaseStage, "get_status", return_value=dummy_base_status)

    session = mocker.Mock()
    stage = SlackProcessStage(
        tenant_id=tenant_id,
        db_session=session,
        processor=slack_processor,
        channel_ids=channel_ids,
        interval_seconds=30,
    )

    status = stage.get_status()

    assert status["base"] is True
    assert status["tenant_id"] == "12345678-1234-5678-1234-************"
    assert status["channels_count"] == len(channel_ids)
    assert status["interval_seconds"] == 30
    assert "metrics" in status

    assert status["metrics"]["total_runs"] == 0
    assert status["metrics"]["successful_runs"] == 0
    assert status["metrics"]["channels_processed"] == 0
    assert status["metrics"]["errors_count"] == 0
