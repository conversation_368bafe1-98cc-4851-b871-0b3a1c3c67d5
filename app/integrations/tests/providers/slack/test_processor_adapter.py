# ruff: noqa: S106
import uuid

import pytest

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.providers.slack.processor_adapter import (
    SlackProcessorAdapter,
)
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = {"token": "xoxb-test-token"}
    return mock_service


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def slack_processor_adapter(mock_context):
    return SlackProcessorAdapter(context=mock_context)


def test_init(mock_context):
    adapter = SlackProcessorAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id


def test_source(slack_processor_adapter):
    assert slack_processor_adapter.source == IntegrationSource.SLACK


def test_start_channel_processing(mocker, slack_processor_adapter, mock_context):
    mock_processing = mocker.patch(
        "app.integrations.providers.slack.dataflow_handler.SlackDataflowHandler.start_channel_processing"
    )
    mock_processing.return_value = {"status": "success"}

    channel_ids = ["C123", "C456"]
    interval_seconds = 600
    batch_size = 200
    daemon_mode = True
    sequential_execution = False

    result = slack_processor_adapter.start_channel_processing(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    mock_processing.assert_called_once_with(
        tenant_id=mock_context.tenant_id,
        channel_ids=channel_ids,
        db_session_factory=mock_context.db_session_factory,
        interval_seconds=interval_seconds,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    assert result == {"status": "success"}
