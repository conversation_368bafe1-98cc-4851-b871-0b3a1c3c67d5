import datetime

import pytest

from app.integrations.processors.readers.channel_changelog_reader import (
    MessageChangeSet,
)
from app.integrations.providers.slack.channel_processor import (
    SlackChannelProcessor,
)
from app.integrations.schemas import ChannelProcessingResult, MessageChangelogData


@pytest.fixture
def slack_processor(mocker):
    channel_changelog_reader = mocker.Mock()
    document_indexer = mocker.Mock()

    # Mock changeset
    mock_changes = [
        MessageChangelogData(
            cursor_id=1,
            message_id="msg1",
            operation="UPDATE",
            channel_id="C12345",
            created_at=datetime.datetime.now(),
        ),
        MessageChangelogData(
            cursor_id=2,
            message_id="msg2",
            operation="INSERT",
            channel_id="C12345",
            created_at=datetime.datetime.now(),
        ),
    ]
    changeset = MessageChangeSet(
        channel_id="C12345",
        consumer_id="test_processor",
        changes=mock_changes,
    )
    channel_changelog_reader.get_next_changes.return_value = changeset

    document_changeset = mocker.Mock()
    document_changeset.documents_to_invalidate = ["doc1", "doc2"]
    document_changeset.documents_to_rebuild = ["doc3", "doc4"]
    document_indexer.determine_document_changes.return_value = document_changeset
    document_indexer.generate_document.return_value = {"generated": True}

    processor = SlackChannelProcessor(
        channel_changelog_reader=channel_changelog_reader,
        document_indexer=document_indexer,
        batch_size=100,
        consumer_id="test_processor",
    )

    return processor


def test_process_channel_success(slack_processor):
    channel_id = "C12345"
    result = slack_processor.process_channel(channel_id)

    assert isinstance(result, ChannelProcessingResult)
    assert result.processed_changes == 2
    assert result.regenerated_documents == 2
    assert result.deleted_documents == 2

    slack_processor.channel_changelog_reader.get_next_changes.assert_called_once_with(
        channel_id="C12345",
        consumer_id="test_processor",
        batch_size=100,
    )
    slack_processor.document_indexer.determine_document_changes.assert_called_once()
    assert slack_processor.document_indexer.invalidate_document.call_count == 2
    assert slack_processor.document_indexer.generate_document.call_count == 2
    slack_processor.channel_changelog_reader.ack.assert_called_once()


def test_process_channel_no_changes(slack_processor):
    empty_changeset = MessageChangeSet(
        channel_id="C12345", consumer_id="test_processor", changes=[]
    )
    slack_processor.channel_changelog_reader.get_next_changes.return_value = (
        empty_changeset
    )

    result = slack_processor.process_channel("C12345")

    assert isinstance(result, ChannelProcessingResult)
    assert result.processed_changes == 0
    assert result.regenerated_documents == 0
    assert result.deleted_documents == 0


def test_process_channel_generate_none(slack_processor):
    slack_processor.document_indexer.generate_document.return_value = None

    result = slack_processor.process_channel("C12345")

    assert result.processed_changes == 2
    assert result.regenerated_documents == 0
    assert result.deleted_documents == 2


def test_process_channel_exception_propagation(slack_processor):
    slack_processor.document_indexer.determine_document_changes.side_effect = Exception(
        "Processing error"
    )

    with pytest.raises(Exception, match="Processing error"):
        slack_processor.process_channel("C12345")
