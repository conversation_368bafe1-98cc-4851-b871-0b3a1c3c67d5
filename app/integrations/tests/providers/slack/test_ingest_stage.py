import datetime
import uuid

from app.common.pipeline.base_stage import BaseStage
from app.integrations.models import SlackIngestionRun
from app.integrations.providers.slack.ingest_stage import SlackIngestStage
from app.integrations.schemas import ChannelIngestionResult


def test_execute_once_success(mocker):
    channel_ids = ["C123", "C456"]

    slack_ingestor = mocker.Mock()

    result1 = ChannelIngestionResult(
        messages_count=1,
        inserts=1,
        updates=0,
        deletes=0,
        from_time=datetime.datetime(2025, 1, 1),
        to_time=datetime.datetime(2025, 1, 2),
    )
    result2 = ChannelIngestionResult(
        messages_count=2,
        inserts=1,
        updates=1,
        deletes=1,
        from_time=datetime.datetime(2025, 2, 1),
        to_time=datetime.datetime(2025, 2, 2),
    )
    slack_ingestor.ingest_channel.side_effect = [result1, result2]
    session = mocker.Mock()
    stage = SlackIngestStage(
        tenant_id=uuid.uuid4(),
        db_session=session,
        ingestor=slack_ingestor,
        channel_ids=channel_ids,
        lookback_days=3,
        interval_seconds=60,
    )

    result = stage.execute_once()

    assert result["status"] == "success"
    assert result["channels_processed"] == 2

    channel_result = result["channels"].get("C123")
    assert channel_result is not None
    assert channel_result["status"] == "success"
    assert channel_result["messages_count"] == 1
    assert channel_result["inserts"] == 1
    assert channel_result["updates"] == 0
    assert channel_result["deletes"] == 0
    assert channel_result["from_time"] == datetime.datetime(2025, 1, 1)
    assert channel_result["to_time"] == datetime.datetime(2025, 1, 2)

    channel_result = result["channels"].get("C456")
    assert channel_result is not None
    assert channel_result["status"] == "success"
    assert channel_result["messages_count"] == 2
    assert channel_result["inserts"] == 1
    assert channel_result["updates"] == 1
    assert channel_result["deletes"] == 1
    assert channel_result["from_time"] == datetime.datetime(2025, 2, 1)
    assert channel_result["to_time"] == datetime.datetime(2025, 2, 2)

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["channels_processed"] == 2
    assert stage.metrics["successful_runs"] == 1
    assert stage.metrics["errors_count"] == 0

    call_kwargs_list = slack_ingestor.ingest_channel.call_args_list
    assert len(call_kwargs_list) == 2

    for call in call_kwargs_list:
        kwargs = call.kwargs
        assert kwargs["channel_id"] in channel_ids
        assert isinstance(kwargs["start_time"], datetime.datetime)
        assert isinstance(kwargs["end_time"], datetime.datetime)
        assert (kwargs["end_time"] - kwargs["start_time"]).days == 3

    assert session.add.call_count == 2
    assert session.commit.call_count == 4


def test_execute_once_partial(mocker, db_session):
    channel_ids = ["C123", "C456"]

    slack_ingestor = mocker.Mock()

    def fake_ingest_channel(channel_id, **_kwargs):
        if channel_id == "C456":
            raise Exception("Execute error")
        return ChannelIngestionResult(
            messages_count=2,
            inserts=1,
            updates=1,
            deletes=1,
            from_time=datetime.datetime(2025, 2, 1),
            to_time=datetime.datetime(2025, 2, 2),
        )

    slack_ingestor.ingest_channel.side_effect = fake_ingest_channel
    stage = SlackIngestStage(
        tenant_id=uuid.uuid4(),
        db_session=db_session,
        ingestor=slack_ingestor,
        channel_ids=channel_ids,
        lookback_days=3,
        interval_seconds=60,
    )

    result = stage.execute_once()

    assert result["status"] == "partial"
    assert result["channels_processed"] == 1

    channel_res_ok = result["channels"].get("C123")
    assert channel_res_ok is not None
    assert channel_res_ok["status"] == "success"

    channel_res_err = result["channels"].get("C456")
    assert channel_res_err is not None
    assert channel_res_err["status"] == "error"
    assert "Execute error" in channel_res_err["error"]

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["channels_processed"] == 1
    assert stage.metrics["successful_runs"] == 0
    assert stage.metrics["errors_count"] == 1

    run_entry_count = db_session.query(SlackIngestionRun).count()
    assert run_entry_count == 2
    run_entry = (
        db_session.query(SlackIngestionRun)
        .filter(SlackIngestionRun.channel_id == "C123")
        .first()
    )
    assert run_entry is not None
    assert run_entry.status == SlackIngestionRun.Status.SUCCESS
    run_entry = (
        db_session.query(SlackIngestionRun)
        .filter(SlackIngestionRun.channel_id == "C456")
        .first()
    )
    assert run_entry is not None
    assert run_entry.status == SlackIngestionRun.Status.FAILED


def test_get_status(mocker):
    channel_ids = ["C123", "C456"]

    slack_ingestor = mocker.Mock()
    tenant_id = uuid.UUID("12345678-1234-5678-1234-************")

    dummy_base_status = {"base": True}
    mocker.patch.object(BaseStage, "get_status", return_value=dummy_base_status)
    session = mocker.Mock()
    stage = SlackIngestStage(
        tenant_id=tenant_id,
        db_session=session,
        ingestor=slack_ingestor,
        channel_ids=channel_ids,
        lookback_days=3,
        interval_seconds=30,
    )

    status = stage.get_status()

    assert status["base"] is True
    assert status["tenant_id"] == "12345678-1234-5678-1234-************"
    assert status["channels_count"] == len(channel_ids)
    assert status["interval_seconds"] == 30
    assert "metrics" in status

    assert status["metrics"]["total_runs"] == 0
    assert status["metrics"]["successful_runs"] == 0
    assert status["metrics"]["channels_processed"] == 0
    assert status["metrics"]["errors_count"] == 0
