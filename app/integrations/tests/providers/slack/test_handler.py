import uuid
from datetime import UTC, datetime

import pytest

from app.integrations.providers.slack.exceptions import Slack<PERSON><PERSON>lerError
from app.integrations.providers.slack.handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.schemas import DocumentData


@pytest.fixture
def slack_handler(mocker):
    tenant_id = uuid.uuid4()

    handler = SlackHandler(
        db_session_factory=lambda: mocker.Mock(),
        tenant_id=tenant_id,
    )

    handler._document_store = mocker.Mock()
    handler._embedder = mocker.Mock()

    return handler


def test_search_channel_messages_success(slack_handler):
    channel_id = "C12345"
    query = "test query"
    query_embedding = [0.1, 0.2, 0.3]

    filtered_mock_documents = [
        (
            DocumentData(
                id="msg1",
                content="Test message 1",
                source_timestamp=datetime(2023, 1, 1, 10, 0, 0, tzinfo=UTC),
                tags={"channel_id:C12345", "message_id_1", "message_id_2"},
            ),
            0.95,
        ),
        (
            DocumentData(
                id="msg2",
                content="Test message 2",
                source_timestamp=datetime(2023, 1, 2, 11, 0, 0, tzinfo=UTC),
                tags={"channel_id:C12345", "message_id_3"},
            ),
            0.85,
        ),
    ]

    slack_handler._embedder.embed_text.return_value = query_embedding
    slack_handler._document_store.find_similar_documents.return_value = (
        filtered_mock_documents
    )

    results = slack_handler.search_channel_messages(channel_id, query, limit=5)

    assert len(results) == 2
    assert results[0][0].id == "msg1"
    assert results[0][1] == 0.95
    assert results[1][0].id == "msg2"
    assert results[1][1] == 0.85

    slack_handler._embedder.embed_text.assert_called_once_with(query)
    slack_handler._document_store.find_similar_documents.assert_called_once_with(
        embedding=query_embedding, limit=5, tag_filter=f"channel_id:{channel_id}"
    )


def test_search_channel_messages_no_results(slack_handler):
    channel_id = "C12345"
    query = "test query"
    query_embedding = [0.1, 0.2, 0.3]

    slack_handler._embedder.embed_text.return_value = query_embedding
    slack_handler._document_store.find_similar_documents.return_value = []

    results = slack_handler.search_channel_messages(channel_id, query)

    assert results == []

    slack_handler._embedder.embed_text.assert_called_once_with(query)
    slack_handler._document_store.find_similar_documents.assert_called_once()


def test_search_channel_messages_error(slack_handler):
    channel_id = "C12345"
    query = "test query"

    slack_handler._embedder.embed_text.side_effect = Exception("Embedding error")

    with pytest.raises(SlackHandlerError, match="Failed to search channel"):
        slack_handler.search_channel_messages(channel_id, query)

    slack_handler._embedder.embed_text.assert_called_once_with(query)
