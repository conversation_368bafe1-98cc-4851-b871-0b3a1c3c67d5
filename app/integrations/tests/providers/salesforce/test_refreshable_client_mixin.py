import pytest

from app.integrations.providers.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)


@pytest.fixture
def mock_credentials(mocker):
    credentials = mocker.MagicMock()
    credentials.refresh_token.return_value = credentials
    return credentials


@pytest.fixture
def oauth_credentials(mock_credentials):
    mock_credentials.secrets = {
        "access_token": "dummy_token",
        "instance_url": "https://test.salesforce.com",
    }
    return mock_credentials


@pytest.fixture
def username_credentials(mock_credentials):
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "dummy_password",
        "security_token": "dummy_token",
    }
    return mock_credentials


@pytest.fixture
def invalid_credentials(mock_credentials):
    mock_credentials.secrets = {"access_token": "dummy_token"}
    return mock_credentials


@pytest.fixture
def mock_salesforce_client(mocker):
    return mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient"
    )


@pytest.fixture
def mock_expired_session(mocker):
    class MockSalesforceExpiredSession(Exception):
        pass

    mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceExpiredSession",
        MockSalesforceExpiredSession,
    )
    return MockSalesforceExpiredSession


class SampleClass(SalesforceRefreshableClientMixin):
    def __init__(self, credentials):
        self.init_salesforce_client(credentials)

    @SalesforceRefreshableClientMixin.handle_expired_session
    def test_method(self, param):
        return f"Success: {param}"


def test_init_salesforce_client_with_oauth(oauth_credentials, mock_salesforce_client):
    test_obj = SampleClass(oauth_credentials)

    mock_salesforce_client.assert_called_once_with(
        instance_url="https://test.salesforce.com",
        access_token=oauth_credentials.secrets["access_token"],
    )

    assert test_obj.credentials == oauth_credentials


def test_init_salesforce_client_with_username_password(
    username_credentials, mock_salesforce_client
):
    test_obj = SampleClass(username_credentials)

    mock_salesforce_client.assert_called_once_with(
        username="<EMAIL>",
        password=username_credentials.secrets["password"],
        security_token=username_credentials.secrets["security_token"],
    )

    assert test_obj.credentials == username_credentials


def test_init_salesforce_client_with_invalid_credentials(invalid_credentials):
    with pytest.raises(ValueError) as excinfo:
        SampleClass(invalid_credentials)

    assert "Missing required 'instance_url'" in str(excinfo.value)


def test_refresh_salesforce_token(mocker, oauth_credentials):
    test_obj = SampleClass(oauth_credentials)

    mock_init = mocker.patch.object(test_obj, "init_salesforce_client")

    refreshed_credentials = mocker.MagicMock()
    refreshed_credentials.secrets = {
        "access_token": "new_token",
        "instance_url": "https://test.salesforce.com",
    }

    oauth_credentials.refresh_token.return_value = refreshed_credentials

    test_obj.refresh_salesforce_token()

    oauth_credentials.refresh_token.assert_called_once()

    mock_init.assert_called_once_with(refreshed_credentials)

    assert test_obj.credentials == refreshed_credentials


def test_handle_expired_session_decorator(mocker, mock_expired_session):
    test_obj = mocker.MagicMock()
    test_obj.refresh_salesforce_token = mocker.MagicMock()

    test_method = mocker.MagicMock()
    test_method.__name__ = "test_method"
    test_method.side_effect = [mock_expired_session(), "Success: test_param"]

    decorated_method = SalesforceRefreshableClientMixin.handle_expired_session(
        test_method
    )
    result = decorated_method(test_obj, "test_param")

    test_obj.refresh_salesforce_token.assert_called_once()
    assert test_method.call_count == 2
    assert result == "Success: test_param"


def test_handle_expired_session_no_exception(mocker, oauth_credentials):
    test_obj = SampleClass(oauth_credentials)

    mock_refresh = mocker.patch.object(test_obj, "refresh_salesforce_token")

    result = test_obj.test_method("test_param")

    mock_refresh.assert_not_called()

    assert result == "Success: test_param"


def test_handle_expired_session_other_exception(mocker, oauth_credentials):
    test_obj = SampleClass(oauth_credentials)

    method_raises_error = mocker.MagicMock(side_effect=ValueError("Some other error"))
    method_raises_error.__name__ = "method_raises_error"

    mock_refresh = mocker.patch.object(test_obj, "refresh_salesforce_token")

    decorated = SalesforceRefreshableClientMixin.handle_expired_session(
        method_raises_error
    )

    with pytest.raises(ValueError) as excinfo:
        decorated(test_obj, "test_param")

    assert "Some other error" in str(excinfo.value)

    mock_refresh.assert_not_called()


class SampleClassWithPostRefresh(SalesforceRefreshableClientMixin):
    def __init__(self, credentials):
        self.init_salesforce_client(credentials)
        self.post_refresh_called = False

    def on_token_refresh(self):
        self.post_refresh_called = True

    @SalesforceRefreshableClientMixin.handle_expired_session
    def test_method(self, param):
        return f"Success: {param}"


def test_refresh_with_post_refresh_callback(mocker, oauth_credentials):
    test_obj = SampleClassWithPostRefresh(oauth_credentials)

    mocker.patch.object(test_obj, "init_salesforce_client")

    refreshed_credentials = mocker.MagicMock()
    refreshed_credentials.secrets = {
        "access_token": "new_token",
        "instance_url": "https://test.salesforce.com",
    }

    oauth_credentials.refresh_token.return_value = refreshed_credentials

    test_obj.refresh_salesforce_token()

    assert test_obj.post_refresh_called is False


def test_handle_expired_session_with_wrapped_exception(mocker, mock_expired_session):
    test_obj = mocker.MagicMock()
    test_obj.refresh_salesforce_token = mocker.MagicMock()

    class MockClientError(Exception):
        pass

    def side_effect_func(*_args, **_kwargs):
        if not hasattr(side_effect_func, "called"):
            side_effect_func.called = True
            inner_exc = mock_expired_session()
            outer_exc = MockClientError("Client error")
            outer_exc.__cause__ = inner_exc
            raise outer_exc
        return "Success: test_param"

    test_method = mocker.MagicMock()
    test_method.__name__ = "test_method"
    test_method.side_effect = side_effect_func

    decorated_method = SalesforceRefreshableClientMixin.handle_expired_session(
        test_method
    )
    result = decorated_method(test_obj, "test_param")

    test_obj.refresh_salesforce_token.assert_called_once()
    assert test_method.call_count == 2
    assert result == "Success: test_param"


def test_handle_expired_session_with_deep_wrapped_exception(
    mocker, mock_expired_session
):
    test_obj = mocker.MagicMock()
    test_obj.refresh_salesforce_token = mocker.MagicMock()

    class MockClientError(Exception):
        pass

    class MockHandlerError(Exception):
        pass

    def side_effect_func(*_args, **_kwargs):
        if not hasattr(side_effect_func, "called"):
            side_effect_func.called = True
            inner_exc = mock_expired_session()
            middle_exc = MockClientError("Client error")
            outer_exc = MockHandlerError("Handler error")
            middle_exc.__cause__ = inner_exc
            outer_exc.__cause__ = middle_exc
            raise outer_exc
        return "Success: test_param"

    test_method = mocker.MagicMock()
    test_method.__name__ = "test_method"
    test_method.side_effect = side_effect_func

    decorated_method = SalesforceRefreshableClientMixin.handle_expired_session(
        test_method
    )
    result = decorated_method(test_obj, "test_param")

    test_obj.refresh_salesforce_token.assert_called_once()
    assert test_method.call_count == 2
    assert result == "Success: test_param"


def test_handle_expired_session_with_raise_from(mocker, mock_expired_session):
    test_obj = mocker.MagicMock()
    test_obj.refresh_salesforce_token = mocker.MagicMock()

    class MockClientError(Exception):
        pass

    def side_effect_func(*_args, **_kwargs):
        if not hasattr(side_effect_func, "called"):
            side_effect_func.called = True
            try:
                raise mock_expired_session("Session expired")
            except Exception as e:
                raise MockClientError("Client error") from e
        return "Success: test_param"

    test_method = mocker.MagicMock()
    test_method.__name__ = "test_method"
    test_method.side_effect = side_effect_func

    decorated_method = SalesforceRefreshableClientMixin.handle_expired_session(
        test_method
    )
    result = decorated_method(test_obj, "test_param")

    test_obj.refresh_salesforce_token.assert_called_once()
    assert test_method.call_count == 2
    assert result == "Success: test_param"


def test_handle_expired_session_retry_also_fails(mocker, mock_expired_session):
    test_obj = mocker.MagicMock()
    test_obj.refresh_salesforce_token = mocker.MagicMock()

    test_method = mocker.MagicMock()
    test_method.__name__ = "test_method"
    test_method.side_effect = [
        mock_expired_session("First failure"),
        mock_expired_session("Second failure"),
    ]

    decorated_method = SalesforceRefreshableClientMixin.handle_expired_session(
        test_method
    )

    with pytest.raises(mock_expired_session) as excinfo:
        decorated_method(test_obj, "test_param")

    assert "Second failure" in str(excinfo.value)
    test_obj.refresh_salesforce_token.assert_called_once()
    assert test_method.call_count == 2
