import uuid

import pytest

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.providers.salesforce.adapter import SalesforceAdapter
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def salesforce_credentials():
    return {
        "username": "test_username",
        "password": "test_password",
        "security_token": "test_token",
    }


@pytest.fixture
def mock_credentials_resolver(mocker, salesforce_credentials):
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = salesforce_credentials
    return mock_service


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture(autouse=True)
def mock_salesforce_handler(mocker):
    mock_handler = mocker.MagicMock()

    mock_handler.get_opportunity.return_value = {
        "Id": "001",
        "Name": "Test Opportunity",
    }
    mock_handler.update_opportunity.return_value = {
        "Id": "001",
        "Name": "Updated Opportunity",
    }
    mock_handler.list_opportunities_by_account.return_value = [
        {"Id": "001", "Name": "Test Opportunity"}
    ]
    mock_handler.get_account.return_value = {"Id": "002", "Name": "Test Account"}
    mock_handler.list_account_access.return_value = [
        {"Id": "002", "Name": "Test Account"}
    ]

    handler_class = mocker.patch(
        "app.integrations.providers.salesforce.adapter.SalesforceHandler"
    )
    handler_class.return_value = mock_handler

    return mock_handler


@pytest.fixture
def salesforce_adapter(mock_context):
    return SalesforceAdapter(context=mock_context)


def test_init(mocker, mock_context):
    handler_class = mocker.patch(
        "app.integrations.providers.salesforce.adapter.SalesforceHandler"
    )

    adapter = SalesforceAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id

    handler_class.assert_called_once_with(
        tenant_id=mock_context.tenant_id,
        db_session_factory=mock_context.db_session_factory,
        credentials_resolver=mock_context.credentials_resolver,
    )


def test_source(salesforce_adapter):
    assert salesforce_adapter.source == IntegrationSource.SALESFORCE


def test_get_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"

    result = salesforce_adapter.get_opportunity(opportunity_id)

    mock_salesforce_handler.get_opportunity.assert_called_once_with(opportunity_id)
    assert result == {"Id": "001", "Name": "Test Opportunity"}


def test_update_opportunity(salesforce_adapter, mock_salesforce_handler):
    opportunity_id = "opp123"
    fields = {"Name": "New Name"}

    result = salesforce_adapter.update_opportunity(opportunity_id, fields)

    mock_salesforce_handler.update_opportunity.assert_called_once_with(
        opportunity_id, fields
    )
    assert result == {"Id": "001", "Name": "Updated Opportunity"}


def test_list_opportunities_by_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "account456"
    limit = 50
    offset = 10

    result = salesforce_adapter.list_opportunities_by_account(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )

    mock_salesforce_handler.list_opportunities_by_account.assert_called_once_with(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )

    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Test Opportunity"


def test_get_account(salesforce_adapter, mock_salesforce_handler):
    account_id = "acc123"

    result = salesforce_adapter.get_account(account_id)

    mock_salesforce_handler.get_account.assert_called_once_with(account_id)
    assert result == {"Id": "002", "Name": "Test Account"}


def test_list_account_access(salesforce_adapter, mock_salesforce_handler):
    user_id = "user123"
    limit = 50
    offset = 10

    result = salesforce_adapter.list_account_access(
        crm_user_id=user_id,
        limit=limit,
        offset=offset,
    )

    mock_salesforce_handler.list_account_access.assert_called_once_with(
        salesforce_user_id=user_id,
        limit=limit,
        offset=offset,
    )

    assert len(result) == 1
    assert result[0]["Id"] == "002"
    assert result[0]["Name"] == "Test Account"
