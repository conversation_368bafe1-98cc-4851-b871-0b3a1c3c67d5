# ruff: noqa: S106
import uuid

import pytest

from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.providers.salesforce.client import SalesforceClientError
from app.integrations.providers.salesforce.handler import (
    SalesforceHandler,
    SalesforceObjectType,
)
from app.integrations.schemas import CRMAccountAccessData, CRMAccountAccessSlice


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "token123",
    }
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = mock_credentials
    return mock_service


@pytest.fixture
def mock_oauth_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = mock_credentials
    return mock_service


@pytest.fixture
def sfdc_handler(mocker, mock_credentials_resolver):
    mock_client = mocker.MagicMock()

    mock_crm_store = mocker.MagicMock()
    mocker.patch(
        "app.integrations.providers.salesforce.handler.PostgresCRMStore",
        return_value=mock_crm_store,
    )

    mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mock_client,
    )

    handler = SalesforceHandler(
        tenant_id=uuid.uuid4(),
        db_session_factory=lambda: mocker.MagicMock(),
        credentials_resolver=mock_credentials_resolver,
    )

    assert handler.salesforce_client is mock_client
    assert handler._crm_store is mock_crm_store

    return handler


def test_init_with_oauth_credentials(mocker, mock_oauth_credentials_resolver):
    mock_client = mocker.MagicMock()
    mocker.patch(
        "app.integrations.providers.salesforce.handler.PostgresCRMStore",
        return_value=mocker.MagicMock(),
    )
    mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mock_client,
    )

    handler = SalesforceHandler(
        tenant_id=uuid.uuid4(),
        db_session_factory=lambda: mocker.MagicMock(),
        credentials_resolver=mock_oauth_credentials_resolver,
    )

    from app.integrations.providers.salesforce.refreshable_client_mixin import (
        SalesforceClient,
    )

    SalesforceClient.assert_called_once_with(
        instance_url="https://test.salesforce.com", access_token="mock_access_token"
    )
    assert handler.salesforce_client is mock_client


def test_init_with_missing_instance_url(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
        # instance_url is missing
    }
    mock_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_resolver.get_credentials.return_value = mock_credentials

    with pytest.raises(ValueError, match="Missing required 'instance_url'"):
        SalesforceHandler(
            tenant_id=uuid.uuid4(),
            db_session_factory=lambda: mocker.MagicMock(),
            credentials_resolver=mock_resolver,
        )


def test_init_with_invalid_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        # password and security_token are missing
    }
    mock_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_resolver.get_credentials.return_value = mock_credentials

    with pytest.raises(ValueError, match="Missing required Salesforce credentials"):
        SalesforceHandler(
            tenant_id=uuid.uuid4(),
            db_session_factory=lambda: mocker.MagicMock(),
            credentials_resolver=mock_resolver,
        )


def test_client_creation_with_username_password(mocker, mock_credentials_resolver):
    client_mock = mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient"
    )
    mocker.patch(
        "app.integrations.providers.salesforce.handler.PostgresCRMStore",
        return_value=mocker.MagicMock(),
    )

    SalesforceHandler(
        tenant_id=uuid.uuid4(),
        db_session_factory=lambda: mocker.MagicMock(),
        credentials_resolver=mock_credentials_resolver,
    )

    client_mock.assert_called_once_with(
        username="<EMAIL>", password="password123", security_token="token123"
    )


def test_get_opportunity_success(sfdc_handler):
    expected_opportunity = {
        "Id": "006XXXXXXXXXXXX",
        "Name": "Test Opportunity",
        "Amount": 50000,
        "CloseDate": "2025-12-31",
        "StageName": "Prospecting",
    }

    sfdc_handler.salesforce_client.get_object.return_value = expected_opportunity

    result = sfdc_handler.get_opportunity("006XXXXXXXXXXXX")

    assert result == expected_opportunity
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY, "006XXXXXXXXXXXX"
    )


def test_get_opportunity_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )

    with pytest.raises(SalesforceClientError, match="Record not found"):
        sfdc_handler.get_opportunity("006XXXXXXXXXXXX")


def test_update_opportunity_success(sfdc_handler):
    opportunity_after = {
        "Id": "006XXXXXXXXXXXX",
        "Name": "Opportunity After Update",
        "Amount": 15000,
        "StageName": "Qualification",
    }

    sfdc_handler.salesforce_client.update_object.return_value = None

    sfdc_handler.salesforce_client.get_object.return_value = opportunity_after

    update_fields = {
        "Name": "Opportunity After Update",
        "Amount": 15000,
        "StageName": "Qualification",
    }

    result = sfdc_handler.update_opportunity("006XXXXXXXXXXXX", update_fields)

    assert result == opportunity_after
    sfdc_handler.salesforce_client.update_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY, "006XXXXXXXXXXXX", update_fields
    )
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY, "006XXXXXXXXXXXX"
    )


def test_update_opportunity_error(sfdc_handler):
    update_fields = {"StageName": "InvalidStage"}

    sfdc_handler.salesforce_client.update_object.side_effect = SalesforceClientError(
        "Invalid field value"
    )

    with pytest.raises(SalesforceClientError, match="Invalid field value"):
        sfdc_handler.update_opportunity("006XXXXXXXXXXXX", update_fields)


def test_list_opportunities_by_account_success(sfdc_handler):
    expected_opportunities = [
        {
            "Id": "006XXXXXXXXXXXX",
            "Name": "Opportunity 1",
            "Amount": 10000,
        },
        {
            "Id": "006YYYYYYYYYYYY",
            "Name": "Opportunity 2",
            "Amount": 20000,
        },
    ]

    sfdc_handler.salesforce_client.list_objects.return_value = expected_opportunities

    result = sfdc_handler.list_opportunities_by_account(
        account_id="001XXXXXXXXXXXX",
        fields="ALL",
        limit=10,
        offset=0,
    )

    assert result == expected_opportunities
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.OPPORTUNITY,
        fields="ALL",
        where_clause="AccountId = '001XXXXXXXXXXXX'",
        order_by=None,
        limit=10,
        offset=0,
    )


def test_list_opportunities_by_account_empty(sfdc_handler):
    result = sfdc_handler.list_opportunities_by_account(account_id="")

    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


def test_list_opportunities_by_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.list_objects.side_effect = SalesforceClientError(
        "Failed to execute query"
    )

    with pytest.raises(SalesforceClientError, match="Failed to execute query"):
        sfdc_handler.list_opportunities_by_account("001XXXXXXXXXXXX")


def test_get_account_success(sfdc_handler):
    expected_account = {
        "Id": "001XXXXXXXXXXXX",
        "Name": "Test Account",
        "Industry": "Technology",
    }

    sfdc_handler.salesforce_client.get_object.return_value = expected_account

    result = sfdc_handler.get_account("001XXXXXXXXXXXX")

    assert result == expected_account
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.ACCOUNT, "001XXXXXXXXXXXX"
    )


def test_get_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )

    with pytest.raises(SalesforceClientError, match="Record not found"):
        sfdc_handler.get_account("001XXXXXXXXXXXX")


def test_list_account_access_success(sfdc_handler):
    mock_account_access_data = [
        CRMAccountAccessData(
            account_id="001XXXXXXXXXXXX",
            account_name="Account 1",
            access_type="owner",
            access_role=None,
        ),
        CRMAccountAccessData(
            account_id="001YYYYYYYYYYYY",
            account_name="Account 2",
            access_type="team",
            access_role="Sales Rep",
        ),
    ]

    mock_access_slice = CRMAccountAccessSlice(
        user_id="005XXXXXXXXXXXX",
        accounts=mock_account_access_data,
    )

    sfdc_handler._crm_store.get_user_account_access.return_value = mock_access_slice

    expected_accounts = [
        {
            "Id": "001XXXXXXXXXXXX",
            "Name": "Account 1",
            "AccessType": "owner",
            "AccessRole": "",
        },
        {
            "Id": "001YYYYYYYYYYYY",
            "Name": "Account 2",
            "AccessType": "team",
            "AccessRole": "Sales Rep",
        },
    ]

    result = sfdc_handler.list_account_access("005XXXXXXXXXXXX")
    assert result == expected_accounts
    sfdc_handler._crm_store.get_user_account_access.assert_called_once_with(
        "005XXXXXXXXXXXX"
    )


def test_list_account_access_pagination(sfdc_handler):
    mock_account_access_data = [
        CRMAccountAccessData(
            account_id=f"001ACCOUNT{i}",
            account_name=f"Account {i}",
            access_type="owner",
            access_role=None,
        )
        for i in range(10)
    ]

    mock_access_slice = CRMAccountAccessSlice(
        user_id="005XXXXXXXXXXXX",
        accounts=mock_account_access_data,
    )

    sfdc_handler._crm_store.get_user_account_access.return_value = mock_access_slice

    result = sfdc_handler.list_account_access("005XXXXXXXXXXXX", 3, 2)

    assert len(result) == 3
    assert result[0]["Id"] == "001ACCOUNT2"
    assert result[1]["Id"] == "001ACCOUNT3"
    assert result[2]["Id"] == "001ACCOUNT4"


def test_list_account_access_error(sfdc_handler):
    sfdc_handler._crm_store.get_user_account_access.side_effect = Exception(
        "Failed to retrieve account access"
    )

    with pytest.raises(Exception, match="Failed to retrieve account access"):
        sfdc_handler.list_account_access("005XXXXXXXXXXXX")
