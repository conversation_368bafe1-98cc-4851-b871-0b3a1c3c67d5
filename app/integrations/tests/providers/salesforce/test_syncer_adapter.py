import uuid

import pytest

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.providers.salesforce.syncer_adapter import (
    SalesforceSyncerAdapter,
)
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def salesforce_credentials(mocker):
    # Create a mock credentials object with a 'secrets' attribute
    credentials = mocker.Mock()
    credentials.secrets = {
        "username": "test_username",
        "password": "test_password",
        "security_token": "test_token",
    }
    return credentials


@pytest.fixture
def mock_credentials_resolver(mocker, salesforce_credentials):
    mock_service = mocker.Mock(spec=ICredentialsResolver)
    mock_service.get_credentials.return_value = salesforce_credentials
    return mock_service


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def salesforce_syncer_adapter(mock_context):
    return SalesforceSyncerAdapter(context=mock_context)


def test_init(mock_context):
    adapter = SalesforceSyncerAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id


def test_source(salesforce_syncer_adapter):
    assert salesforce_syncer_adapter.source == IntegrationSource.SALESFORCE


def test_bulk_sync_account_access(mocker, salesforce_syncer_adapter, mock_context):
    mock_handler_instance = mocker.Mock()
    mock_handler_instance.bulk_sync_account_access.return_value = {"status": "success"}

    mock_handler_class = mocker.patch(
        "app.integrations.providers.salesforce.syncer_adapter.SalesforceSyncHandler"
    )
    mock_handler_class.return_value = mock_handler_instance

    user_ids = ["user1", "user2"]
    interval_seconds = 600
    daemon_mode = True

    result = salesforce_syncer_adapter.bulk_sync_account_access(
        crm_user_ids=user_ids,
        interval_seconds=interval_seconds,
        daemon_mode=daemon_mode,
    )

    mock_handler_class.assert_called_once_with(
        tenant_id=mock_context.tenant_id,
        db_session_factory=mock_context.db_session_factory,
        credentials_resolver=mock_context.credentials_resolver,
    )

    assert mock_handler_instance.bulk_sync_account_access.call_count == 1

    call_args = mock_handler_instance.bulk_sync_account_access.call_args.kwargs
    assert call_args.get("salesforce_user_ids") == user_ids
    assert call_args.get("interval_seconds") == interval_seconds
    assert call_args.get("daemon_mode") == daemon_mode

    assert result == {"status": "success"}
