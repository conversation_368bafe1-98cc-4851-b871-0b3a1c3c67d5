import uuid

import pytest

from app.integrations.providers.salesforce.access_synchronizer import (
    SalesforceAccountAccessSynchronizer,
)
from app.integrations.schemas import (
    CRMAccountAccessData,
    CRMAccountAccessSlice,
    CRMAccountAccessSyncResult,
)


def test_sync_user_access_success(mocker):
    tenant_id = uuid.uuid4()
    user_id = "user123"

    access_resolver_mock = mocker.Mock()
    crm_store_mock = mocker.Mock()

    account_access_records = [
        CRMAccountAccessData(
            account_id="account1",
            account_name="Account One",
            access_type="owner",
            access_role="Manager",
        ),
        CRMAccountAccessData(
            account_id="account2",
            account_name="Account Two",
            access_type="team",
            access_role="Member",
        ),
    ]
    access_resolver_mock.get_user_account_access.return_value = account_access_records

    crm_store_mock.store_account_access.return_value = (2, 1)

    synchronizer = SalesforceAccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=crm_store_mock,
        access_resolver=access_resolver_mock,
    )

    result = synchronizer.sync_user_access(user_id)

    access_resolver_mock.get_user_account_access.assert_called_once_with(user_id)

    crm_store_mock.store_account_access.assert_called_once()

    call_args = crm_store_mock.store_account_access.call_args[0]
    assert isinstance(call_args[0], CRMAccountAccessSlice)
    assert call_args[0].user_id == user_id
    assert len(call_args[0].accounts) == 2

    assert isinstance(result, CRMAccountAccessSyncResult)
    assert result.new_access_count == 2
    assert result.old_access_count == 1


def test_sync_user_access_with_empty_records(mocker):
    tenant_id = uuid.uuid4()
    user_id = "user123"

    access_resolver_mock = mocker.Mock()
    crm_store_mock = mocker.Mock()

    access_resolver_mock.get_user_account_access.return_value = []

    crm_store_mock.store_account_access.return_value = (0, 0)

    synchronizer = SalesforceAccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=crm_store_mock,
        access_resolver=access_resolver_mock,
    )

    result = synchronizer.sync_user_access(user_id)

    access_resolver_mock.get_user_account_access.assert_called_once_with(user_id)

    crm_store_mock.store_account_access.assert_called_once()

    call_args = crm_store_mock.store_account_access.call_args[0]
    assert isinstance(call_args[0], CRMAccountAccessSlice)
    assert call_args[0].user_id == user_id
    assert len(call_args[0].accounts) == 0

    assert isinstance(result, CRMAccountAccessSyncResult)
    assert result.new_access_count == 0
    assert result.old_access_count == 0


def test_sync_user_access_resolver_failure(mocker):
    tenant_id = uuid.uuid4()
    user_id = "user123"

    access_resolver_mock = mocker.Mock()
    crm_store_mock = mocker.Mock()

    access_resolver_mock.get_user_account_access.side_effect = Exception(
        "Resolution error"
    )

    synchronizer = SalesforceAccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=crm_store_mock,
        access_resolver=access_resolver_mock,
    )

    with pytest.raises(Exception, match="Resolution error"):
        synchronizer.sync_user_access(user_id)

    access_resolver_mock.get_user_account_access.assert_called_once_with(user_id)

    crm_store_mock.store_account_access.assert_not_called()


def test_sync_user_access_store_failure(mocker):
    tenant_id = uuid.uuid4()
    user_id = "user123"

    access_resolver_mock = mocker.Mock()
    crm_store_mock = mocker.Mock()

    account_access_records = [
        CRMAccountAccessData(
            account_id="account1",
            account_name="Account One",
            access_type="owner",
            access_role="Manager",
        )
    ]
    access_resolver_mock.get_user_account_access.return_value = account_access_records

    crm_store_mock.store_account_access.side_effect = Exception("Storage error")

    synchronizer = SalesforceAccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=crm_store_mock,
        access_resolver=access_resolver_mock,
    )

    with pytest.raises(Exception, match="Storage error"):
        synchronizer.sync_user_access(user_id)

    access_resolver_mock.get_user_account_access.assert_called_once_with(user_id)

    crm_store_mock.store_account_access.assert_called_once()
