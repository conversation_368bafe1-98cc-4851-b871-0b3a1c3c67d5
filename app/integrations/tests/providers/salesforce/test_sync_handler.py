# ruff: noqa: S106

import uuid
from unittest.mock import call

import pytest

from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.providers.salesforce.sync_handler import (
    SalesforceSyncHandler,
)


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "token123",
    }
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value = mock_credentials
    return mock_credentials_resolver


@pytest.fixture
def mock_oauth_credentials_resolver(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "fake-token",
        "instance_url": "https://fake.salesforce.com",
    }
    mock_credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials_resolver.get_credentials.return_value = mock_credentials
    return mock_credentials_resolver


@pytest.fixture
def db_session_factory():
    def factory():
        return None

    return factory


@pytest.fixture
def sync_handler(mock_credentials_resolver, db_session_factory):
    tenant_id = uuid.uuid4()
    return SalesforceSyncHandler(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_credentials_resolver,
    )


def test_constructor(mock_credentials_resolver, db_session_factory):
    tenant_id = uuid.uuid4()

    handler = SalesforceSyncHandler(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert handler.tenant_id == tenant_id
    assert handler.db_session_factory == db_session_factory
    assert handler.credentials_resolver == mock_credentials_resolver
    assert handler._synchronizer_cache == {}


def test_create_synchronizer_with_password_auth(
    mocker, sync_handler, mock_credentials_resolver
):
    mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mocker.Mock(),
    )

    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessResolver",
        return_value=mocker.Mock(),
    )

    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.PostgresCRMStore",
        return_value=mocker.Mock(),
    )

    mock_synchronizer = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessSynchronizer",
        return_value=mock_synchronizer,
    )

    result = sync_handler._create_synchronizer(mock_credentials_resolver)

    assert result == mock_synchronizer


def test_create_synchronizer_with_oauth(
    mocker, mock_oauth_credentials_resolver, db_session_factory
):
    tenant_id = uuid.uuid4()

    handler = SalesforceSyncHandler(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_oauth_credentials_resolver,
    )

    mocker.patch(
        "app.integrations.providers.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mocker.Mock(),
    )

    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessResolver",
        return_value=mocker.Mock(),
    )

    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.PostgresCRMStore",
        return_value=mocker.Mock(),
    )

    mock_synchronizer = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessSynchronizer",
        return_value=mock_synchronizer,
    )

    result = handler._create_synchronizer(mock_oauth_credentials_resolver)

    assert result == mock_synchronizer


def test_get_or_create_synchronizer_creates_new(
    mocker, sync_handler, mock_credentials_resolver
):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_create_synchronizer", return_value=mock_synchronizer
    )

    result = sync_handler._get_or_create_synchronizer(mock_credentials_resolver)

    assert result == mock_synchronizer
    sync_handler._create_synchronizer.assert_called_once_with(mock_credentials_resolver)

    resolver_id = id(mock_credentials_resolver)
    assert sync_handler._synchronizer_cache[resolver_id] == mock_synchronizer


def test_get_or_create_synchronizer_returns_from_cache(
    mocker, sync_handler, mock_credentials_resolver
):
    mock_synchronizer = mocker.Mock()
    resolver_id = id(mock_credentials_resolver)
    sync_handler._synchronizer_cache[resolver_id] = mock_synchronizer

    create_mock = mocker.patch.object(sync_handler, "_create_synchronizer")

    result = sync_handler._get_or_create_synchronizer(mock_credentials_resolver)

    assert result == mock_synchronizer
    create_mock.assert_not_called()


def test_bulk_sync_account_access_with_default_resolver(mocker, sync_handler):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_get_or_create_synchronizer", return_value=mock_synchronizer
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mock_pipeline.run.return_value = {"status": "success"}
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    user_ids = ["user1", "user2"]
    result = sync_handler.bulk_sync_account_access(
        salesforce_user_ids=user_ids,
        interval_seconds=600,
        daemon_mode=False,
    )

    assert sync_handler._get_or_create_synchronizer.call_count == 2
    sync_handler._get_or_create_synchronizer.assert_has_calls(
        [
            call(sync_handler.credentials_resolver),
            call(sync_handler.credentials_resolver),
        ]
    )

    from app.integrations.providers.salesforce.sync_handler import (
        SalesforceAccountAccessSyncStage,
    )

    SalesforceAccountAccessSyncStage.assert_called_once()
    _, kwargs = SalesforceAccountAccessSyncStage.call_args
    assert kwargs["tenant_id"] == sync_handler.tenant_id
    assert kwargs["interval_seconds"] == 600
    assert len(kwargs["user_synchronizers"]) == 2
    assert kwargs["user_synchronizers"] == [
        ("user1", mock_synchronizer),
        ("user2", mock_synchronizer),
    ]

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.run.assert_called_once()
    assert result == {"status": "success"}


def test_bulk_sync_account_access_with_custom_resolver(mocker, sync_handler):
    mock_synchronizer1 = mocker.Mock(name="synchronizer1")
    mock_synchronizer2 = mocker.Mock(name="synchronizer2")

    get_or_create_mock = mocker.patch.object(
        sync_handler,
        "_get_or_create_synchronizer",
        side_effect=[mock_synchronizer1, mock_synchronizer2],
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mock_pipeline.run.return_value = {"status": "success"}
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    resolver1 = mocker.Mock(name="resolver1")
    resolver2 = mocker.Mock(name="resolver2")

    def custom_get_credentials_resolver(user_id):
        if user_id == "user1":
            return resolver1
        else:
            return resolver2

    user_ids = ["user1", "user2"]
    result = sync_handler.bulk_sync_account_access(
        salesforce_user_ids=user_ids,
        get_credentials_resolver=custom_get_credentials_resolver,
        interval_seconds=600,
        daemon_mode=False,
    )

    assert get_or_create_mock.call_count == 2
    get_or_create_mock.assert_has_calls([call(resolver1), call(resolver2)])

    from app.integrations.providers.salesforce.sync_handler import (
        SalesforceAccountAccessSyncStage,
    )

    SalesforceAccountAccessSyncStage.assert_called_once()
    _, kwargs = SalesforceAccountAccessSyncStage.call_args
    assert kwargs["tenant_id"] == sync_handler.tenant_id
    assert kwargs["interval_seconds"] == 600
    assert len(kwargs["user_synchronizers"]) == 2
    assert kwargs["user_synchronizers"] == [
        ("user1", mock_synchronizer1),
        ("user2", mock_synchronizer2),
    ]

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.run.assert_called_once()
    assert result == {"status": "success"}


def test_bulk_sync_account_access_daemon_mode(mocker, sync_handler):
    mock_synchronizer = mocker.Mock()
    mocker.patch.object(
        sync_handler, "_get_or_create_synchronizer", return_value=mock_synchronizer
    )

    mock_stage = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.SalesforceAccountAccessSyncStage",
        return_value=mock_stage,
    )

    mock_pipeline = mocker.Mock()
    mocker.patch(
        "app.integrations.providers.salesforce.sync_handler.PipelineRunner",
        return_value=mock_pipeline,
    )

    user_ids = ["user1"]
    result = sync_handler.bulk_sync_account_access(
        salesforce_user_ids=user_ids,
        daemon_mode=True,
    )

    mock_pipeline.add_stage.assert_called_once_with(mock_stage)
    mock_pipeline.start_daemon.assert_called_once()
    assert result == {"status": "daemon_stopped"}


def test_bulk_sync_account_access_no_resolver(sync_handler):
    sync_handler.credentials_resolver = None

    with pytest.raises(
        ValueError, match="Either get_credentials_resolver must be provided"
    ):
        sync_handler.bulk_sync_account_access(
            salesforce_user_ids=["user1"],
        )


def test_bulk_sync_account_access_user_resolver_returns_none(sync_handler):
    def bad_resolver(_):
        return None

    with pytest.raises(ValueError, match="Missing credentials resolver for user"):
        sync_handler.bulk_sync_account_access(
            salesforce_user_ids=["user1"],
            get_credentials_resolver=bad_resolver,
        )


def test_bulk_sync_account_access_no_valid_synchronizers(mocker, sync_handler):
    mocker.patch.object(sync_handler, "_get_or_create_synchronizer", return_value=None)

    with pytest.raises(ValueError, match="No valid synchronizers could be created"):
        sync_handler.bulk_sync_account_access(
            salesforce_user_ids=["user1", "user2"],
        )
