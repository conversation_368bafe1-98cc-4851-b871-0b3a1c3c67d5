from logging.config import fileConfig

# todo: to be reviewed, it doesn't seem the right way to do it
# from app.....models import *
from alembic import context
from alembic.autogenerate import renderers
from sqlalchemy import engine_from_config, pool

from app.common.orm import types
from app.core.config import config as app_config
from app.core.database import BaseModel

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
from app.workspace.models import *
from app.auth.models import *
from app.integrations.models import *

# target_metadata = mymodel.Base.metadata
target_metadata = BaseModel.metadata


# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

config.set_main_option("sqlalchemy.url", str(app_config.database.database_url))


# LangGraph tables
TABLES_TO_IGNORE = {
    "checkpoint_migrations",
    "checkpoints",
    "checkpoint_blobs",
    "checkpoint_writes",
}


def include_object(object, name, type_, reflected, compare_to):
    """
    Filter function for Alembic autogenerate to ignore LangGraph tables and indexes.
    """
    if type_ == "table" and reflected and name in TABLES_TO_IGNORE:
        return False

    if (
        type_ != "table"
        and reflected
        and hasattr(object, "table")
        and object.table.name in TABLES_TO_IGNORE
    ):
        return False

    return True

def render_item(type_, obj, autogen_context):
    if type_ == 'type' and isinstance(obj, types.StringEnum):
        return f"sa.{repr(obj.impl)}"
    return False

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
        render_item=render_item,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,
            render_item=render_item,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
